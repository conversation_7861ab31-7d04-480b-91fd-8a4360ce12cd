# Agent Setup Scripts Documentation
**Automated Environment Setup for Remote AI Agents**

## 🎯 **Overview**

The agent setup scripts automate the complete environment preparation process for remote AI agents working on the Royaltea platform. These scripts validate the repository structure, install dependencies, configure the development environment, and create personalized agent workspaces.

---

## 📁 **Available Scripts**

### **Cross-Platform Launcher**
- **File**: `scripts/setup-agent.cmd`
- **Purpose**: Automatically detects OS and runs appropriate setup script
- **Usage**: Double-click or run from command line
- **Platforms**: Windows, Unix/Linux, macOS

### **Windows PowerShell Script**
- **File**: `scripts/setup-remote-agent.ps1`
- **Purpose**: Full setup automation for Windows environments
- **Requirements**: PowerShell 5.0+, Windows 10+
- **Features**: Colored output, comprehensive validation, error handling

### **Unix/Linux/macOS Bash Script**
- **File**: `scripts/setup-remote-agent.sh`
- **Purpose**: Full setup automation for Unix-based systems
- **Requirements**: Bash 4.0+, standard Unix tools
- **Features**: Colored output, cross-platform compatibility

---

## 🚀 **Quick Start**

### **Method 1: Simple Launcher (Recommended)**
```bash
# Navigate to repository root
cd /path/to/royaltea

# Run cross-platform launcher
./scripts/setup-agent.cmd
```

### **Method 2: Direct Script Execution**

#### **Windows (PowerShell)**
```powershell
# Navigate to repository root
cd C:\path\to\royaltea

# Run PowerShell script
powershell -ExecutionPolicy Bypass -File scripts/setup-remote-agent.ps1

# With parameters
powershell -ExecutionPolicy Bypass -File scripts/setup-remote-agent.ps1 -AgentId "agent-001" -Verbose
```

#### **Unix/Linux/macOS (Bash)**
```bash
# Navigate to repository root
cd /path/to/royaltea

# Make script executable (first time only)
chmod +x scripts/setup-remote-agent.sh

# Run bash script
./scripts/setup-remote-agent.sh

# With parameters
./scripts/setup-remote-agent.sh --agent-id "agent-001" --verbose
```

---

## ⚙️ **Script Parameters**

### **PowerShell Script Parameters**
```powershell
-AgentId "string"        # Set unique agent identifier
-SkipValidation          # Skip final build validation
-Verbose                 # Enable detailed output
```

### **Bash Script Parameters**
```bash
--agent-id ID           # Set unique agent identifier
--skip-validation       # Skip final build validation
--verbose              # Enable detailed output
-h, --help             # Show help information
```

### **Parameter Examples**
```bash
# Set specific agent ID
./scripts/setup-agent.cmd --agent-id "agent-frontend-001"

# Skip build validation (faster setup)
./scripts/setup-agent.cmd --skip-validation

# Enable verbose output for debugging
./scripts/setup-agent.cmd --verbose

# Combine parameters
./scripts/setup-agent.cmd --agent-id "agent-001" --verbose --skip-validation
```

---

## 🔍 **What the Scripts Do**

### **Phase 1: Repository Validation**
- ✅ Validates repository structure and required files
- ✅ Checks for essential documentation (PRD, design system, wireframes)
- ✅ Verifies frontend and backend directory structure
- ✅ Ensures all agent framework files are present

### **Phase 2: Development Tools Check**
- ✅ Validates Node.js installation and version
- ✅ Checks NPM package manager availability
- ✅ Verifies Git version control system
- ✅ Reports tool versions for compatibility

### **Phase 3: Dependency Installation**
- ✅ Installs frontend dependencies via NPM
- ✅ Validates package installation success
- ✅ Checks for environment configuration files
- ✅ Reports installation status and any issues

### **Phase 4: Environment Configuration**
- ✅ Creates personalized agent workspace directory
- ✅ Generates agent configuration file (JSON)
- ✅ Sets up development environment variables
- ✅ Validates design system access

### **Phase 5: Agent Workspace Setup**
- ✅ Creates unique workspace: `agent-workspace/[agent-id]/`
- ✅ Generates personalized quick reference guide
- ✅ Creates agent configuration with setup details
- ✅ Provides next steps and essential reading list

### **Phase 6: Final Validation (Optional)**
- ✅ Tests build process to ensure environment works
- ✅ Validates all components are properly configured
- ✅ Reports any potential issues or warnings
- ✅ Confirms setup completion status

---

## 📋 **Generated Files**

### **Agent Workspace Structure**
```
agent-workspace/[agent-id]/
├── agent-config.json          # Agent configuration and setup details
├── quick-reference.md          # Personalized quick reference guide
├── task-progress.md           # Task tracking (created during work)
└── daily-reports/             # Daily status reports directory
```

### **Agent Configuration File**
```json
{
  "agentId": "agent-frontend-001",
  "setupDate": "2025-01-16 14:30:00",
  "repository": "Royaltea Platform",
  "workspaceDirectory": "agent-workspace/agent-frontend-001",
  "requiredReading": [
    "docs/design-system/agent-quick-start.md",
    "docs/PRODUCT_REQUIREMENTS.md",
    "docs/design-system/coding-instructions.md"
  ],
  "availableTasks": "docs/design-system/agent-task-queue.md",
  "reportingFormat": "docs/design-system/remote-agent-setup.md#progress-reporting-system"
}
```

### **Quick Reference Guide**
- Personalized guide with agent ID and setup timestamp
- Essential reading list with time estimates
- Key requirements and success criteria
- Design pattern explanations
- Available tasks and next steps
- Emergency escalation procedures

---

## 🛠️ **Troubleshooting**

### **Common Issues**

#### **"Repository structure validation failed"**
- **Cause**: Not running from correct repository root
- **Solution**: Navigate to Royaltea repository root directory
- **Check**: Ensure `docs/PRODUCT_REQUIREMENTS.md` exists

#### **"Required tools missing"**
- **Cause**: Node.js, NPM, or Git not installed
- **Solution**: Install missing tools:
  - Node.js: https://nodejs.org/
  - Git: https://git-scm.com/
- **Check**: Run `node --version` and `npm --version`

#### **"Failed to install frontend dependencies"**
- **Cause**: Network issues or package conflicts
- **Solution**: 
  - Check internet connection
  - Clear NPM cache: `npm cache clean --force`
  - Delete `node_modules` and retry

#### **"Build validation failed"**
- **Cause**: Environment configuration issues
- **Solution**: 
  - Check `.env.local` file exists in `client/` directory
  - Verify environment variables are set correctly
  - Use `--skip-validation` flag to bypass

### **Script Execution Issues**

#### **PowerShell Execution Policy Error**
```powershell
# Fix execution policy
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Or run with bypass
powershell -ExecutionPolicy Bypass -File scripts/setup-remote-agent.ps1
```

#### **Bash Permission Denied**
```bash
# Make script executable
chmod +x scripts/setup-remote-agent.sh

# Or run with bash directly
bash scripts/setup-remote-agent.sh
```

---

## 🎯 **Post-Setup Next Steps**

### **Immediate Actions**
1. **Read Quick Reference**: Review generated `quick-reference.md`
2. **Complete Essential Reading**: 30 minutes total
   - Agent Quick Start Guide (15 min)
   - Product Requirements (5 min)
   - Coding Instructions (10 min)
3. **Select First Task**: Choose from task queue
4. **Submit Task Request**: Follow format in documentation

### **Validation Checklist**
- [ ] Agent workspace created successfully
- [ ] Configuration file generated
- [ ] Quick reference guide available
- [ ] Essential documentation accessible
- [ ] Development environment functional
- [ ] Ready to select and begin first task

---

## 📞 **Support**

### **Setup Issues**
- Check troubleshooting section above
- Verify all prerequisites are installed
- Ensure running from correct repository directory

### **Agent Framework Questions**
- Review agent documentation in `docs/design-system/`
- Check task queue for available work
- Reference coding instructions for implementation rules

### **Emergency Escalation**
- Mark any urgent issues in daily status reports
- Reference emergency procedures in agent documentation
- Coordinate with other agents for integration issues

---

**The setup scripts provide a complete, automated onboarding experience for remote AI agents, ensuring consistent environment configuration and immediate productivity.**
