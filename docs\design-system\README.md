# Royaltea Design System
**Design-to-Code Pipeline Documentation**

## 🎯 **For Design Team: Quick Start**

This directory is your **single source of truth** for all design decisions. Update any file here, and coding agents will automatically implement the changes in the codebase.

### **📁 Design Team Workflow**
1. **Update Design Files** → Design team modifies files in this directory
2. **Commit Changes** → Push to repository
3. **Automatic Implementation** → Coding agents read changes and update code
4. **Review & Deploy** → Validate implementation and deploy

---

## 📚 **Design Documentation Files**

### **🎨 Core Design System**
- **[colors.md](colors.md)** - Color palette, themes, usage guidelines
- **[typography.md](typography.md)** - Font system, hierarchy, sizing
- **[icons.md](icons.md)** - Icon library, usage, specifications
- **[components.md](components.md)** - Component specifications & behavior
- **[animations.md](animations.md)** - Motion design, transitions, timing

### **📐 Layout & Structure**
- **[bento-grid.md](bento-grid.md)** - ✅ Three-column layout system & widget standards
- **[spacing.md](spacing.md)** - Padding, margins, layout spacing
- **[responsive.md](responsive.md)** - Breakpoints & responsive behavior

### **📋 Product Requirements Integration**
- **[../../PRODUCT_REQUIREMENTS.md](../../PRODUCT_REQUIREMENTS.md)** - Master product specification (integrates with all systems)
- **[prd-integration.md](prd-integration.md)** - How PRD connects to design system

### **🎨 Design Assets**
- **[../design-team-assets/](../design-team-assets/)** - ⭐ **DESIGN TEAM WORKSPACE** - Your centralized design location
- **[asset-management.md](asset-management.md)** - Complete asset organization and workflow
- **[asset-processing-instructions.md](asset-processing-instructions.md)** - How dev team processes design assets

### **🏗️ System Architecture**
- **[alliance-system.md](systems/alliance-system.md)** - Alliance/team management system
- **[social-system.md](systems/social-system.md)** - Friend requests, messaging, collaboration
- **[gamification-system.md](systems/gamification-system.md)** - ORB currency, achievements, progression
- **[navigation-system.md](systems/navigation-system.md)** - Spatial navigation, canvas system
- **[payment-system.md](systems/payment-system.md)** - Plaid integration, escrow, revenue sharing
- **[vetting-education-system.md](systems/vetting-education-system.md)** - 6-level skill verification and learning management

### **🔧 Implementation Guides**
- **[design-tokens.json](design-tokens.json)** - Automated design tokens
- **[component-mapping.md](component-mapping.md)** - Design → Code mapping
- **[coding-instructions.md](coding-instructions.md)** - Instructions for agents

---

## 🚀 **How to Update Designs**

### **🎨 For Visual Changes (Colors, Typography, Components)**
1. Edit the appropriate design system file:
   - `colors.md` - Color palette updates
   - `typography.md` - Font and text styling
   - `components.md` - UI component specifications
2. Coding agents automatically update:
   - Tailwind config and CSS variables
   - Component styling and themes
   - Responsive behavior
   - Accessibility features

### **🏗️ For New Systems (Alliance, Social, Gamification, etc.)**
1. Create or update system file: `systems/[system-name].md`
2. Include complete specifications:
   - System overview and features
   - User interface design (ASCII wireframes)
   - User experience flows
   - Data requirements
3. Coding agents automatically:
   - Create React components
   - Set up database schema
   - Implement API endpoints
   - Add routing and navigation

### **📐 For Wireframes and Layouts**
1. Update wireframe files in `../wireframes/`
2. Include detailed component breakdowns
3. Specify responsive behavior
4. Coding agents automatically:
   - Create page layouts
   - Implement component structure
   - Apply responsive design
   - Add interaction behaviors

### **For Icons**
1. Add SVG files to `../../client/src/assets/icons/`
2. Update `icons.md` with usage guidelines
3. Coding agents will automatically:
   - Optimize SVG files
   - Create React icon components
   - Update icon library exports
   - Generate usage documentation

---

## 🚀 **Quick Examples**

### **Adding a New System (e.g., Social Features)**
1. Create `systems/social-system.md`
2. Document features: friend requests, messaging, endorsements
3. Include ASCII wireframes of interfaces
4. Specify user flows and data requirements
5. Commit → AI implements complete social system

### **Changing Visual Design**
1. Edit `colors.md` with new brand colors
2. Update `typography.md` with new font hierarchy
3. Commit → All components automatically update

### **Creating New Components**
1. Add component spec to `components.md`
2. Include visual design, states, and behavior
3. Commit → Component created and integrated everywhere

---

## 📚 **Complete Documentation Framework**

### **🎯 Design Team Resources**
- **[Design Team Workflow](design-team-workflow.md)** - Complete guide for design-driven development
- **[Asset Management](asset-management.md)** - How to organize icons, wireframes, and mockups
- **[Coding Instructions](coding-instructions.md)** - How AI agents implement your designs

### **🤖 Remote Agent Framework**
- **[Remote Agent Setup](remote-agent-setup.md)** - Framework for independent AI agent development
- **[Agent Task Queue](agent-task-queue.md)** - Centralized task management for remote agents
- **[Agent Coordination](agent-coordination.md)** - Multi-agent collaboration system

### **📋 Documentation Standards**

#### **File Organization**
- **Systems**: One file per major feature in `systems/` directory
- **Components**: Reusable UI elements in `components.md`
- **Visual Design**: Colors, typography, spacing in dedicated files
- **Assets**: Icons, wireframes, mockups in organized asset structure

#### **Content Requirements**
Every system documentation must include:
- **System Overview** - Purpose, features, user benefits
- **User Interface Design** - ASCII wireframes and visual specifications
- **User Experience Flow** - Step-by-step user journeys
- **Data Requirements** - Database schema and API endpoints
- **Responsive Behavior** - Mobile, tablet, desktop adaptations
- **Accessibility Features** - Screen reader and keyboard support

#### **Quality Standards**
- **Complete Specifications** - No guesswork for implementation
- **Visual Clarity** - ASCII art, measurements, exact values
- **User-Centered** - Focus on user goals and workflows
- **Technical Precision** - Exact data structures and API requirements

---

## 🤖 **For Coding Agents**

### **Implementation Priority**
1. **Design Tokens** - Always implement token changes first
2. **Core Components** - Update base components before variants
3. **Layout Systems** - Grid and spacing changes affect everything
4. **Theming** - Color and typography updates last

### **Automatic Implementation Rules**
- Read all `.md` files in this directory for specifications
- Check `design-tokens.json` for programmatic values
- Validate against wireframes in `../wireframes/`
- Maintain backward compatibility unless explicitly noted
- Generate TypeScript types for all design tokens
- Update Storybook documentation automatically

### **File Change Triggers**
- `colors.md` → Update Tailwind config, CSS variables, theme files
- `typography.md` → Update font imports, text utilities, component styles
- `icons.md` → Generate icon components, update exports
- `components.md` → Create/update React components
- `animations.md` → Update motion utilities, transition classes

---

**This design system enables seamless collaboration between design and development teams, ensuring consistent implementation of design decisions across the entire Royaltea platform.**
