import React from 'react';
import { Card, CardBody, CardHeader } from '../../components/ui/heroui';

/**
 * MissionBoardPage Component
 * 
 * Placeholder for the Mission Board feature
 * This will be implemented as part of the mission system development
 */
const MissionBoardPage = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-foreground mb-8">
          🎯 Mission Board
        </h1>
        
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">Coming Soon</h2>
          </CardHeader>
          <CardBody>
            <p className="text-default-600">
              The Mission Board feature is currently under development. This will be where you can:
            </p>
            <ul className="list-disc list-inside mt-4 space-y-2 text-default-600">
              <li>Browse available missions from your alliances</li>
              <li>Accept and track mission progress</li>
              <li>View mission rewards and requirements</li>
              <li>Collaborate with team members on missions</li>
            </ul>
            <p className="mt-4 text-sm text-default-500">
              This feature will be available in a future update.
            </p>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default MissionBoardPage;
