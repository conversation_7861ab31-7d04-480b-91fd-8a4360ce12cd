# Bento Grid System
**Royaltea Platform Layout & Wireframe Standards**

## 🎯 **Layout Philosophy**

The Royaltea platform uses a **three-column layout system** with a central bento grid and contextual helper sidebars. This creates an intuitive, consistent user experience across all platform areas.

---

## 📐 **Standard Layout Pattern**

### **Core Layout Structure**
```
┌─────┐ ┌─────────────────────────────────────────────────────────┐ ┌─────┐
│     │ │                    PAGE TITLE                           │ │     │
│     │ │                                                         │ │     │
│ L   │ │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │ R   │
│ E   │ │  │   WIDGET    │ │   WIDGET    │ │   WIDGET    │       │ │ I   │
│ F   │ │  │             │ │             │ │             │       │ │ G   │
│ T   │ │  └─────────────┘ └─────────────┘ └─────────────┘       │ │ H   │
│     │ │                                                         │ │ T   │
│ S   │ │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │     │
│ I   │ │  │   WIDGET    │ │   WIDGET    │ │   WIDGET    │       │ │ S   │
│ D   │ │  │             │ │             │ │             │       │ │ I   │
│ E   │ │  └─────────────┘ └─────────────┘ └─────────────┘       │ │ D   │
│     │ │                                                         │ │ E   │
└─────┘ └─────────────────────────────────────────────────────────┘ └─────┘
```

---

## 🔧 **Component Specifications**

### **Left Sidebar (Persistent Helpers)**
**Width**: 60px
**Purpose**: Always-available global actions
**Contents**:
- 🔔 **Notifications** - Global notification bell
- 📧 **Messages** - Direct messages access
- 📋 **Tasks** - Quick task overview
- 💬 **Chat** - Team communication
- ⚙️ **Settings** - User preferences

### **Center Area (Bento Grid)**
**Width**: Flexible (remaining space)
**Purpose**: Main content and widgets
**Grid System**:
- **1x1**: Small widgets (status, quick stats)
- **2x1**: Medium widgets (lists, forms)
- **2x2**: Large widgets (dashboards, detailed views)
- **3x1**: Wide widgets (timelines, progress bars)
- **4x1**: Full-width widgets (charts, tables)

### **Right Sidebar (Contextual Actions)**
**Width**: 60px
**Purpose**: Context-specific quick actions
**Contents vary by page**:

**Projects Context**:
- ➕ Add Project
- ⬇️ Import/Export
- 🗑️ Archive
- 📋 Reports

**Tasks Context**:
- ➕ Quick Add
- ✓ Bulk Actions
- 📊 Analytics
- 🔍 Search

**Revenue Context**:
- 📄 Generate Report
- 📅 Schedule Payment
- ⚙️ Settings
- 📤 Export

---

## 🎨 **Widget Design Standards**

### **Widget Header Pattern**
```
┌─────────────────────────────────────┐
│ 📊 Widget Title           [Action]  │
├─────────────────────────────────────┤
│                                     │
│         Widget Content              │
│                                     │
└─────────────────────────────────────┘
```

### **Widget Sizing Rules**
- **Minimum**: 200px width, 150px height
- **Padding**: 16px internal padding
- **Spacing**: 12px between widgets
- **Border Radius**: 8px for modern appearance
- **Responsive**: Stack vertically on mobile

---

## 📱 **Responsive Behavior**

### **Desktop (> 1024px)**
- Full three-column layout
- All widgets visible
- Hover states and tooltips

### **Tablet (768px - 1024px)**
- Collapsed sidebars (icons only)
- 2-column widget grid
- Touch-optimized interactions

### **Mobile (< 768px)**
- Single column layout
- Bottom navigation bar
- Swipeable widget cards
- Simplified contextual actions

---

## 🎯 **Implementation Guidelines**

### **For All System Wireframes**
1. **Always use the three-column pattern**
2. **Left sidebar**: Persistent global actions
3. **Center**: Bento grid with appropriate widgets
4. **Right sidebar**: Context-specific actions
5. **Consistent spacing and sizing**

### **Widget Content Guidelines**
- **Clear hierarchy**: Title, content, actions
- **Scannable information**: Key metrics prominent
- **Actionable elements**: Clear buttons and links
- **Status indicators**: Visual feedback for states

---

## ✅ **Wireframe Checklist**

When creating system wireframes:
- [ ] Uses three-column layout pattern
- [ ] Left sidebar has persistent helpers
- [ ] Right sidebar has contextual actions
- [ ] Widgets follow sizing standards
- [ ] Mobile responsive behavior specified
- [ ] All interactive elements clearly marked
- [ ] Consistent with existing wireframes

---

**This bento grid system ensures consistent, intuitive navigation and layout across the entire Royaltea platform while maintaining flexibility for different content types and user workflows.**
