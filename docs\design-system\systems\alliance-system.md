# Alliance System
**Complete System Specification v1.0**

## 📋 Document Information
- **Last Updated**: January 16, 2025
- **Version**: 1.0
- **Implementation Status**: 🔴 Not Implemented
- **Priority**: 🔥 Critical (VRC dependency)

---

## 🎯 System Overview

The Alliance System replaces the traditional "Teams" concept with a gamified, flexible organizational structure that supports both established businesses (like VRC) and gaming collaboratives.

### **Key Features**
- **Alliance Creation**: Form persistent organizations with governance structures
- **Member Management**: Role-based permissions and responsibilities
- **Business Models**: Support multiple revenue and collaboration models
- **Venture Management**: Create and manage projects within alliances
- **Social Integration**: Connect with allies and build professional networks

### **User Benefits**
- Clear organizational structure for collaborative work
- Flexible business model configuration
- Professional networking and reputation building
- Transparent revenue sharing and governance

---

## 🏗️ Architecture

### **Core Components**
```
Alliance System
├── Alliance Management
│   ├── Alliance Creation Wizard
│   ├── Alliance Dashboard
│   ├── Member Management
│   └── Settings & Configuration
├── Business Model Engine
│   ├── Revenue Model Configuration
│   ├── Commission Tracking
│   ├── Subscription Management
│   └── Financial Reporting
├── Venture Integration
│   ├── Venture Creation
│   ├── Project Management
│   ├── Task Assignment
│   └── Progress Tracking
└── Social Features
    ├── Alliance Discovery
    ├── Member Invitations
    ├── Collaboration Tools
    └── Communication Hub
```

### **Data Flow**
1. **Alliance Creation** → Business model selection → Member invitation
2. **Venture Creation** → Alliance assignment → Member role assignment
3. **Work Completion** → Revenue calculation → Distribution per alliance rules
4. **Member Activity** → Reputation tracking → Alliance performance metrics

---

## 🎨 User Interface Design

### **Alliance Dashboard (Main View)**
```
┌─────┐ ┌─────────────────────────────────────────────────────────┐ ┌─────┐
│ 🔔  │ │                   Alliance Dashboard                    │ │ ➕  │
│     │ │                                                         │ │New  │
│ 📧  │ │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │Vent │
│     │ │  │🏰 My Alliance│ │📊 Performance│ │👥 Team Status│      │ │     │
│ 📋  │ │  │ VRC Studios │ │ Revenue: $12k│ │ 8 Members   │       │ │ 👥  │
│     │ │  │ 3 Ventures  │ │ Growth: +15% │ │ 2 Pending   │       │ │Inv  │
│ 👥  │ │  └─────────────┘ └─────────────┘ └─────────────┘       │ │     │
│     │ │                                                         │ │ 📊  │
│ ⚙️  │ │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │Rep  │
│     │ │  │📋 Active Tasks│ │💰 Revenue   │ │🎯 Goals     │       │ │orts │
│     │ │  │ 12 In Progress│ │ This Month  │ │ Q1 Targets  │       │ │     │
│     │ │  │ 3 Overdue   │ │ $8,450 paid │ │ 75% Complete│       │ │ ⚙️  │
│     │ │  └─────────────┘ └─────────────┘ └─────────────┘       │ │Set  │
└─────┘ └─────────────────────────────────────────────────────────┘ └─────┘
```

### **Alliance Creation Wizard**
```
┌─────┐ ┌─────────────────────────────────────────────────────────┐ ┌─────┐
│ 🔔  │ │                Create New Alliance                      │ │ 💾  │
│     │ │                                                         │ │Save │
│ 📧  │ │  Step 1 of 4: Alliance Basics                          │ │Draft│
│     │ │                                                         │ │     │
│ 📋  │ │  Alliance Name: [________________________]             │ │ 📋  │
│     │ │                                                         │ │Temp │
│ 👥  │ │  Industry: [Dropdown: Film, Gaming, Software...]       │ │late │
│     │ │                                                         │ │     │
│ ⚙️  │ │  Type: [○ Business ○ Collective ○ Startup]             │ │ ❓  │
│     │ │                                                         │ │Help │
│     │ │  Description:                                           │ │     │
│     │ │  [_____________________________________________]        │ │     │
│     │ │  [_____________________________________________]        │ │     │
│     │ │                                                         │ │     │
│     │ │                    [Cancel] [Next: Business Model]     │ │     │
└─────┘ └─────────────────────────────────────────────────────────┘ └─────┘
```

### **Member Management Interface**
```
┌─────┐ ┌─────────────────────────────────────────────────────────┐ ┌─────┐
│ 🔔  │ │                  Alliance Members                       │ │ ➕  │
│     │ │                                                         │ │Inv  │
│ 📧  │ │  ┌─────────────────────────────────────────────────────┐ │ │ite │
│     │ │  │ 👑 John Doe (Owner)                                 │ │ │     │
│ 📋  │ │  │ <EMAIL> | Joined: Jan 2025                 │ │ │ 📧  │
│     │ │  │ [Edit Role] [View Profile] [Message]                │ │ │Bulk │
│ 👥  │ │  └─────────────────────────────────────────────────────┘ │ │Inv  │
│     │ │                                                         │ │     │
│ ⚙️  │ │  ┌─────────────────────────────────────────────────────┐ │ │ 🗑️  │
│     │ │  │ 🛡️ Sarah Smith (Admin)                              │ │ │Rem  │
│     │ │  │ <EMAIL> | Joined: Jan 2025                │ │ │ove │
│     │ │  │ [Edit Role] [View Profile] [Message]                │ │ │     │
│     │ │  └─────────────────────────────────────────────────────┘ │ │ ⚙️  │
│     │ │                                                         │ │Role │
│     │ │  ┌─────────────────────────────────────────────────────┐ │ │Mgmt │
└─────┘ └─────────────────────────────────────────────────────────┘ └─────┘
```
│    <EMAIL>                                │
│    Joined: Jan 2025                                │
│                                                     │
│ Pending Invitations:                               │
│ 📧 <EMAIL> (sent 2 days ago) [Resend]    │
└─────────────────────────────────────────────────────┘
```

---

## 🔄 User Experience Flow

### **Alliance Creation Flow**
```mermaid
graph TD
    A[Start Alliance Creation] --> B[Alliance Basics]
    B --> C[Business Model Selection]
    C --> D[Revenue Configuration]
    D --> E[Member Roles Setup]
    E --> F[Review & Create]
    F --> G[Alliance Dashboard]
    G --> H[Invite Members]
    H --> I[Create First Venture]
```

### **Member Invitation Flow**
```mermaid
graph TD
    A[Click Invite Members] --> B[Enter Email/Username]
    B --> C[Select Role]
    C --> D[Add Personal Message]
    D --> E[Send Invitation]
    E --> F[Invitation Sent]
    F --> G[Track Invitation Status]
    G --> H[Member Accepts]
    H --> I[Welcome to Alliance]
```

---

## 📊 Data Requirements

### **Database Schema**
```sql
-- Alliances table
CREATE TABLE alliances (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    industry VARCHAR(100),
    alliance_type VARCHAR(50), -- 'business', 'collective', 'startup'
    business_model JSONB, -- Revenue model configuration
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    owner_id UUID REFERENCES auth.users(id)
);

-- Alliance members table
CREATE TABLE alliance_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    alliance_id UUID REFERENCES alliances(id),
    user_id UUID REFERENCES auth.users(id),
    role VARCHAR(50), -- 'owner', 'admin', 'member'
    permissions JSONB,
    joined_at TIMESTAMP DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'active' -- 'active', 'inactive', 'pending'
);

-- Alliance invitations table
CREATE TABLE alliance_invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    alliance_id UUID REFERENCES alliances(id),
    email VARCHAR(255),
    role VARCHAR(50),
    invited_by UUID REFERENCES auth.users(id),
    message TEXT,
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'accepted', 'declined', 'expired'
    created_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP
);
```

### **API Endpoints Required**
```
POST   /api/alliances                    # Create alliance
GET    /api/alliances                    # List user's alliances
GET    /api/alliances/:id                # Get alliance details
PUT    /api/alliances/:id                # Update alliance
DELETE /api/alliances/:id                # Delete alliance

POST   /api/alliances/:id/members        # Invite member
GET    /api/alliances/:id/members        # List members
PUT    /api/alliances/:id/members/:uid   # Update member role
DELETE /api/alliances/:id/members/:uid   # Remove member

POST   /api/alliances/:id/invitations    # Send invitation
GET    /api/alliances/:id/invitations    # List invitations
PUT    /api/invitations/:id/accept       # Accept invitation
PUT    /api/invitations/:id/decline      # Decline invitation
```

---

## 🔧 Technical Implementation

### **Component Structure**
```
client/src/components/alliance/
├── AllianceCreationWizard.jsx
├── AllianceDashboard.jsx
├── AllianceSettings.jsx
├── MemberManagement.jsx
├── MemberInvitation.jsx
├── BusinessModelConfig.jsx
├── AllianceCard.jsx (for bento grid)
└── AllianceSelector.jsx
```

### **State Management**
```javascript
// Alliance context
const AllianceContext = {
  currentAlliance: null,
  userAlliances: [],
  members: [],
  invitations: [],
  businessModel: {},
  permissions: {}
};

// Key actions
- createAlliance(data)
- updateAlliance(id, data)
- inviteMember(allianceId, email, role)
- updateMemberRole(allianceId, userId, role)
- acceptInvitation(invitationId)
```

---

## 🧪 Testing Requirements

### **User Acceptance Criteria**
- [ ] User can create alliance with business model selection
- [ ] User can invite members with specific roles
- [ ] Members can accept/decline invitations
- [ ] Alliance owner can manage member permissions
- [ ] Revenue model configuration works correctly
- [ ] Alliance dashboard shows accurate metrics

### **Edge Cases**
- [ ] Handle duplicate alliance names
- [ ] Manage invitation expiration
- [ ] Handle member removal from active ventures
- [ ] Validate business model configurations
- [ ] Handle alliance deletion with active ventures

---

## 📱 Responsive Behavior

### **Mobile Adaptations**
- Alliance cards stack vertically in mobile view
- Member management uses swipe actions
- Simplified creation wizard with fewer fields per screen
- Touch-optimized role selection interface

### **Tablet Considerations**
- Two-column layout for member management
- Expanded alliance cards with more details
- Side-by-side wizard steps where appropriate

---

## ♿ Accessibility Features

- **Screen Reader Support**: All alliance information announced clearly
- **Keyboard Navigation**: Full keyboard access to all functions
- **High Contrast**: Alliance status and roles clearly distinguishable
- **Focus Management**: Clear focus indicators throughout workflows
- **Alternative Text**: Meaningful descriptions for all visual elements

---

**This specification serves as the complete guide for implementing the Alliance System. All development should follow these exact requirements.**
