import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate, useParams } from 'react-router-dom';
import { UserContext } from '../../../../contexts/supabase-auth.context';
import { useSupabase } from '../../hooks/useSupabase';
import { toast } from 'react-hot-toast';
import { Button } from '@heroui/react';

// Import question flow components
import VentureWelcomeScreen from './VentureWelcomeScreen';
import VentureQuestionFlow from './VentureQuestionFlow';
import VentureReviewScreen from './VentureReviewScreen';

// Import existing wizard components for Phase 2
import TeamContributors from '../project/wizard/TeamContributors';
import RoyaltyModel from '../project/wizard/RoyaltyModel';
import RevenueTranches from '../project/wizard/RevenueTranchesEnhanced';
import ContributionTracking from '../project/wizard/ContributionTracking';
import Milestones from '../project/wizard/Milestones';
import ReviewAgreement from '../project/wizard/ReviewAgreement';

/**
 * VentureSetupWizard Component
 * 
 * Enhanced venture creation wizard that combines:
 * - Phase 1: Immersive question flow (8 simple questions)
 * - Phase 2: Existing detailed wizard steps
 * 
 * Follows immersive pattern for questions, then transitions to bento grid
 * for detailed configuration while maintaining all existing functionality.
 */
const VentureSetupWizard = ({ 
  mode = 'create', // 'create' or 'edit'
  allianceId = null,
  onComplete,
  onCancel 
}) => {
  const { currentUser } = useContext(UserContext);
  const { supabase } = useSupabase();
  const navigate = useNavigate();
  const { id } = useParams();
  
  // Wizard state management
  const [currentPhase, setCurrentPhase] = useState('welcome'); // 'welcome', 'questions', 'detailed', 'review'
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [completedSteps, setCompletedSteps] = useState([]);
  
  // Question flow data (Phase 1)
  const [questionData, setQuestionData] = useState({
    projectCategory: '',
    projectSubtype: '',
    targetAudience: '',
    timeline: '',
    budget: '',
    successMetrics: '',
    ventureName: '',
    ventureDescription: '',
    ventureIcon: '🚀',
    ventureTags: [],
    initialTeamRoles: {}
  });
  
  // Detailed wizard data (Phase 2) - matches existing ProjectWizard structure
  const [projectData, setProjectData] = useState({
    // Basic project info (populated from questions)
    name: '',
    description: '',
    project_type: '',
    start_date: new Date(),
    launch_date: null,
    estimated_duration: 6,
    thumbnail_url: '',
    is_public: true,
    
    // Alliance connection
    team_id: allianceId,
    
    // Company Information
    company_name: '',
    company_address: '',
    company_state: '',
    company_county: '',
    
    // Project-Specific Fields
    engine: '',
    platforms: '',
    genre: '',
    technology_stack: '',
    distribution_platforms: '',
    
    // Team & Contributors
    contributors: [],
    
    // Royalty Model
    royalty_model: {
      model_type: 'custom',
      model_schema: 'cog',
      configuration: {
        tasks_weight: 33.33,
        hours_weight: 33.33,
        difficulty_weight: 33.34
      },
      is_pre_expense: true
    },
    
    // Revenue Tranches
    revenue_tranches: [],
    
    // Contribution Tracking
    contribution_tracking: {
      categories: ['Design', 'Development', 'Content', 'Management'],
      difficulty_scale: [1, 2, 3, 5, 8],
      task_types: []
    },
    
    // Milestones
    milestones: []
  });

  // Load existing project data if editing
  useEffect(() => {
    if (mode === 'edit' && id) {
      loadExistingProject();
    }
  }, [mode, id]);

  const loadExistingProject = async () => {
    setIsLoading(true);
    try {
      const { data: project, error } = await supabase
        .from('projects')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;

      if (project) {
        setProjectData(project);
        // Skip question flow for existing projects
        setCurrentPhase('detailed');
      }
    } catch (error) {
      console.error('Error loading project:', error);
      toast.error('Failed to load project data');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle question flow completion
  const handleQuestionsComplete = (answers) => {
    setQuestionData(answers);
    
    // Map question answers to project data
    const mappedProjectData = mapQuestionsToProjectData(answers);
    setProjectData(prev => ({ ...prev, ...mappedProjectData }));
    
    // Transition to detailed wizard
    setCurrentPhase('detailed');
    setCurrentStep(2); // Skip ProjectBasics since questions replace it
    setCompletedSteps([1]); // Mark step 1 as completed
  };

  // Map question answers to project data structure
  const mapQuestionsToProjectData = (answers) => {
    const mapped = {
      name: answers.ventureName,
      description: answers.ventureDescription,
      project_type: mapCategoryToProjectType(answers.projectCategory, answers.projectSubtype),
      estimated_duration: mapTimelineToMonths(answers.timeline),
      start_date: new Date(),
      thumbnail_url: '', // Could map icon to thumbnail
      is_public: true
    };

    // Map budget to royalty model suggestions
    if (answers.budget) {
      mapped.royalty_model = mapBudgetToRoyaltyModel(answers.budget);
    }

    return mapped;
  };

  // Helper functions for mapping
  const mapCategoryToProjectType = (category, subtype) => {
    const mapping = {
      'software': subtype || 'app',
      'creative': 'art',
      'business': 'consulting',
      'physical': 'product',
      'other': 'other'
    };
    return mapping[category] || 'other';
  };

  const mapTimelineToMonths = (timeline) => {
    const mapping = {
      'quick': 1,
      'short': 3,
      'medium': 8,
      'long': 18,
      'flexible': 6
    };
    return mapping[timeline] || 6;
  };

  const mapBudgetToRoyaltyModel = (budget) => {
    // Different royalty models based on budget approach
    const models = {
      'funded': {
        model_type: 'custom',
        model_schema: 'cog',
        configuration: { tasks_weight: 40, hours_weight: 40, difficulty_weight: 20 }
      },
      'bootstrapped': {
        model_type: 'custom', 
        model_schema: 'cog',
        configuration: { tasks_weight: 30, hours_weight: 30, difficulty_weight: 40 }
      },
      'sweat_equity': {
        model_type: 'custom',
        model_schema: 'cog', 
        configuration: { tasks_weight: 25, hours_weight: 35, difficulty_weight: 40 }
      },
      'revenue_first': {
        model_type: 'custom',
        model_schema: 'cog',
        configuration: { tasks_weight: 35, hours_weight: 25, difficulty_weight: 40 }
      }
    };
    return models[budget] || models['sweat_equity'];
  };

  // Handle detailed wizard step navigation
  const handleDetailedNext = () => {
    if (currentStep < 7) {
      setCurrentStep(currentStep + 1);
      if (!completedSteps.includes(currentStep)) {
        setCompletedSteps([...completedSteps, currentStep]);
      }
    }
  };

  const handleDetailedPrevious = () => {
    if (currentStep > 2) {
      setCurrentStep(currentStep - 1);
    } else {
      // Go back to questions
      setCurrentPhase('questions');
    }
  };

  // Handle wizard completion
  const handleWizardComplete = async () => {
    setIsLoading(true);
    try {
      // Save the complete project
      const { data: project, error } = await supabase
        .from('projects')
        .insert([projectData])
        .select()
        .single();

      if (error) throw error;

      toast.success('Venture created successfully!');
      
      if (onComplete) {
        onComplete({
          type: 'venture_created',
          venture: project,
          action: 'create_mission',
          redirectTo: `/project/${project.id}`
        });
      } else {
        navigate(`/project/${project.id}`);
      }
    } catch (error) {
      console.error('Error creating venture:', error);
      toast.error('Failed to create venture. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Render current phase
  const renderCurrentPhase = () => {
    switch (currentPhase) {
      case 'welcome':
        return (
          <VentureWelcomeScreen
            allianceId={allianceId}
            onStart={() => setCurrentPhase('questions')}
            onCancel={onCancel}
          />
        );
      
      case 'questions':
        return (
          <VentureQuestionFlow
            initialData={questionData}
            onComplete={handleQuestionsComplete}
            onBack={() => setCurrentPhase('welcome')}
            onCancel={onCancel}
          />
        );
      
      case 'detailed':
        return renderDetailedWizard();
      
      case 'review':
        return (
          <VentureReviewScreen
            questionData={questionData}
            projectData={projectData}
            onConfirm={handleWizardComplete}
            onBack={() => setCurrentPhase('detailed')}
            isLoading={isLoading}
          />
        );
      
      default:
        return null;
    }
  };

  // Render detailed wizard steps (existing functionality)
  const renderDetailedWizard = () => {
    const renderStep = () => {
      switch (currentStep) {
        case 2:
          return (
            <TeamContributors
              projectData={projectData}
              setProjectData={setProjectData}
              projectId={id}
            />
          );
        case 3:
          return (
            <RoyaltyModel
              projectData={projectData}
              setProjectData={setProjectData}
            />
          );
        case 4:
          return (
            <RevenueTranches
              projectData={projectData}
              setProjectData={setProjectData}
            />
          );
        case 5:
          return (
            <ContributionTracking
              projectData={projectData}
              setProjectData={setProjectData}
            />
          );
        case 6:
          return (
            <Milestones
              projectData={projectData}
              setProjectData={setProjectData}
            />
          );
        case 7:
          return (
            <ReviewAgreement
              projectData={projectData}
              setProjectData={setProjectData}
              projectId={id}
            />
          );
        default:
          return null;
      }
    };

    return (
      <div className="container mx-auto mt-4 mb-5 px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-center mb-4 text-3xl font-bold text-foreground">
            Configure Your Venture
          </h1>
          
          {/* Progress indicator */}
          <div className="mb-6">
            <div className="flex justify-center space-x-2">
              {[2, 3, 4, 5, 6, 7].map((step) => (
                <div
                  key={step}
                  className={`w-3 h-3 rounded-full ${
                    step === currentStep
                      ? 'bg-primary'
                      : completedSteps.includes(step)
                      ? 'bg-success'
                      : 'bg-default-300'
                  }`}
                />
              ))}
            </div>
            <p className="text-center text-sm text-default-500 mt-2">
              Step {currentStep - 1} of 6
            </p>
          </div>

          {/* Step content */}
          <div className="bg-content1 rounded-lg p-6">
            {renderStep()}
            
            {/* Navigation */}
            <div className="flex justify-between mt-6">
              <Button
                variant="flat"
                onPress={handleDetailedPrevious}
                disabled={isLoading}
              >
                Previous
              </Button>

              <div className="space-x-2">
                {currentStep < 7 ? (
                  <Button
                    onPress={handleDetailedNext}
                    disabled={isLoading}
                  >
                    Next
                  </Button>
                ) : (
                  <Button
                    onPress={() => setCurrentPhase('review')}
                    disabled={isLoading}
                    className="bg-success text-white"
                  >
                    Review & Create
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50">
      <AnimatePresence mode="wait">
        {renderCurrentPhase()}
      </AnimatePresence>
    </div>
  );
};

export default VentureSetupWizard;
