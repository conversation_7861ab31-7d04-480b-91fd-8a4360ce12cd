# Remote Agent Setup Script
# Automated environment setup and validation for AI agents working on Royaltea platform

param(
    [string]$AgentId = "",
    [switch]$SkipValidation = $false,
    [switch]$Verbose = $false
)

# Script configuration
$ErrorActionPreference = "Stop"
$ProgressPreference = "SilentlyContinue"

# Colors for output
$Colors = @{
    Success = "Green"
    Warning = "Yellow" 
    Error = "Red"
    Info = "Cyan"
    Header = "Magenta"
}

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Colors[$Color]
}

function Write-Header {
    param([string]$Title)
    Write-Host ""
    Write-ColorOutput "=" * 60 -Color "Header"
    Write-ColorOutput "  $Title" -Color "Header"
    Write-ColorOutput "=" * 60 -Color "Header"
    Write-Host ""
}

function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

function Test-FileExists {
    param([string]$Path, [string]$Description)
    if (Test-Path $Path) {
        Write-ColorOutput "✅ $Description found: $Path" -Color "Success"
        return $true
    }
    else {
        Write-ColorOutput "❌ $Description missing: $Path" -Color "Error"
        return $false
    }
}

function Get-AgentId {
    if ($AgentId -eq "") {
        Write-ColorOutput "Please enter a unique Agent ID (e.g., 'agent-frontend-001'):" -Color "Info"
        $AgentId = Read-Host
        if ($AgentId -eq "") {
            Write-ColorOutput "Agent ID is required. Exiting." -Color "Error"
            exit 1
        }
    }
    return $AgentId
}

# Main setup process
Write-Header "🤖 ROYALTEA REMOTE AGENT SETUP"

Write-ColorOutput "Setting up development environment for remote AI agent..." -Color "Info"
Write-ColorOutput "Repository: Royaltea Platform (Design-Driven Development)" -Color "Info"

# Get Agent ID
$AgentId = Get-AgentId
Write-ColorOutput "Agent ID: $AgentId" -Color "Success"

# Step 1: Validate repository structure
Write-Header "📁 REPOSITORY STRUCTURE VALIDATION"

$RequiredPaths = @{
    "docs/PRODUCT_REQUIREMENTS.md" = "Product Requirements Document"
    "docs/design-system/README.md" = "Design System Documentation"
    "docs/design-system/coding-instructions.md" = "Coding Instructions"
    "docs/design-system/agent-quick-start.md" = "Agent Quick Start Guide"
    "docs/design-system/agent-task-queue.md" = "Agent Task Queue"
    "docs/wireframes/README.md" = "Wireframes Documentation"
    "client/package.json" = "Frontend Package Configuration"
    "client/src" = "Frontend Source Directory"
    "netlify/functions" = "Backend Functions Directory"
}

$ValidationPassed = $true
foreach ($path in $RequiredPaths.Keys) {
    if (-not (Test-FileExists $path $RequiredPaths[$path])) {
        $ValidationPassed = $false
    }
}

if (-not $ValidationPassed) {
    Write-ColorOutput "❌ Repository structure validation failed. Please ensure you're in the correct Royaltea repository." -Color "Error"
    exit 1
}

Write-ColorOutput "✅ Repository structure validated successfully" -Color "Success"

# Step 2: Check required tools
Write-Header "🛠️ DEVELOPMENT TOOLS VALIDATION"

$RequiredTools = @{
    "node" = "Node.js (required for frontend development)"
    "npm" = "NPM (package manager)"
    "git" = "Git (version control)"
}

$ToolsValid = $true
foreach ($tool in $RequiredTools.Keys) {
    if (Test-Command $tool) {
        $version = & $tool --version 2>$null
        Write-ColorOutput "✅ $($RequiredTools[$tool]): $version" -Color "Success"
    }
    else {
        Write-ColorOutput "❌ $($RequiredTools[$tool]) not found" -Color "Error"
        $ToolsValid = $false
    }
}

if (-not $ToolsValid) {
    Write-ColorOutput "❌ Required tools missing. Please install missing tools and run setup again." -Color "Error"
    exit 1
}

# Step 3: Install dependencies
Write-Header "📦 DEPENDENCY INSTALLATION"

Write-ColorOutput "Installing frontend dependencies..." -Color "Info"
Set-Location "client"

try {
    npm install
    Write-ColorOutput "✅ Frontend dependencies installed successfully" -Color "Success"
}
catch {
    Write-ColorOutput "❌ Failed to install frontend dependencies: $($_.Exception.Message)" -Color "Error"
    exit 1
}

Set-Location ".."

# Step 4: Environment configuration
Write-Header "⚙️ ENVIRONMENT CONFIGURATION"

# Check for environment files
$EnvFiles = @(
    "client/.env.local",
    "client/.env.example"
)

foreach ($envFile in $EnvFiles) {
    Test-FileExists $envFile "Environment file" | Out-Null
}

# Create agent workspace
$AgentWorkspace = "agent-workspace/$AgentId"
if (-not (Test-Path $AgentWorkspace)) {
    New-Item -ItemType Directory -Path $AgentWorkspace -Force | Out-Null
    Write-ColorOutput "✅ Created agent workspace: $AgentWorkspace" -Color "Success"
}

# Create shared coordination directory
$SharedWorkspace = "agent-workspace/shared"
if (-not (Test-Path $SharedWorkspace)) {
    New-Item -ItemType Directory -Path $SharedWorkspace -Force | Out-Null
    Write-ColorOutput "✅ Created shared coordination workspace: $SharedWorkspace" -Color "Success"
}

# Ensure shared task status file exists
$TaskStatusFile = "$SharedWorkspace/task-status.md"
if (-not (Test-Path $TaskStatusFile)) {
    Write-ColorOutput "⚠️ Shared task status file not found. Please ensure agent-workspace/shared/task-status.md exists." -Color "Warning"
}

# Step 5: Validate design system access
Write-Header "🎨 DESIGN SYSTEM VALIDATION"

$DesignSystemFiles = @{
    "docs/design-system/bento-grid.md" = "Design Patterns"
    "docs/design-system/colors.md" = "Color System"
    "docs/design-system/typography.md" = "Typography System"
    "docs/design-system/spacing.md" = "Spacing System"
    "client/src/assets/design-system" = "Design Assets"
}

foreach ($file in $DesignSystemFiles.Keys) {
    Test-FileExists $file $DesignSystemFiles[$file] | Out-Null
}

# Step 6: Create agent configuration
Write-Header "📋 AGENT CONFIGURATION"

$AgentConfig = @{
    AgentId = $AgentId
    SetupDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Repository = "Royaltea Platform"
    WorkspaceDirectory = $AgentWorkspace
    RequiredReading = @(
        "docs/design-system/agent-quick-start.md",
        "docs/PRODUCT_REQUIREMENTS.md",
        "docs/design-system/coding-instructions.md"
    )
    AvailableTasks = "docs/design-system/agent-task-queue.md"
    ReportingFormat = "docs/design-system/remote-agent-setup.md#progress-reporting-system"
}

$ConfigPath = "$AgentWorkspace/agent-config.json"
$AgentConfig | ConvertTo-Json -Depth 3 | Out-File -FilePath $ConfigPath -Encoding UTF8
Write-ColorOutput "✅ Agent configuration saved: $ConfigPath" -Color "Success"

# Step 7: Generate quick reference
$QuickRef = @"
# $AgentId Quick Reference
Generated: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

## Essential Reading (Complete in order):
1. docs/design-system/agent-quick-start.md (15 minutes)
2. docs/PRODUCT_REQUIREMENTS.md (5 minutes)
3. docs/design-system/coding-instructions.md (10 minutes)

## Key Requirements:
- Design Fidelity: 95%+ match to wireframes
- Performance: <5 minutes to first meaningful action
- Test Coverage: >90%
- Accessibility: WCAG 2.1 AA compliance

## Design Patterns:
- Bento Grid: Dashboards/management (3-column layout)
- Immersive Flow: Onboarding/creation (full-screen, minimal UI)

## Available Tasks:
See: docs/design-system/agent-task-queue.md

## Daily Reporting:
Required format in: docs/design-system/remote-agent-setup.md

## Emergency Escalation:
- Technical Issues: Report in daily status
- Design Questions: Reference design documentation
- Urgent Blockers: Mark status as "URGENT"

## Workspace Directory:
$AgentWorkspace

## Next Steps:
1. Read essential documentation
2. Select task from queue
3. Submit task request
4. Begin implementation following 5-phase process
"@

$QuickRefPath = "$AgentWorkspace/quick-reference.md"
$QuickRef | Out-File -FilePath $QuickRefPath -Encoding UTF8
Write-ColorOutput "✅ Quick reference guide created: $QuickRefPath" -Color "Success"

# Step 8: Final validation
if (-not $SkipValidation) {
    Write-Header "🔍 FINAL VALIDATION"
    
    # Test build process
    Write-ColorOutput "Testing build process..." -Color "Info"
    Set-Location "client"
    
    try {
        npm run build 2>$null
        Write-ColorOutput "✅ Build process validated successfully" -Color "Success"
    }
    catch {
        Write-ColorOutput "⚠️ Build validation failed - may need environment configuration" -Color "Warning"
    }
    
    Set-Location ".."
}

# Setup complete
Write-Header "🎉 SETUP COMPLETE"

Write-ColorOutput "Remote Agent setup completed successfully!" -Color "Success"
Write-ColorOutput ""
Write-ColorOutput "Agent ID: $AgentId" -Color "Info"
Write-ColorOutput "Workspace: $AgentWorkspace" -Color "Info"
Write-ColorOutput "Configuration: $ConfigPath" -Color "Info"
Write-ColorOutput "Quick Reference: $QuickRefPath" -Color "Info"
Write-ColorOutput ""
Write-ColorOutput "NEXT STEPS:" -Color "Header"
Write-ColorOutput "1. Read the quick reference guide: $QuickRefPath" -Color "Info"
Write-ColorOutput "2. Complete essential reading (30 minutes total)" -Color "Info"
Write-ColorOutput "3. Select a task from: docs/design-system/agent-task-queue.md" -Color "Info"
Write-ColorOutput "4. Submit task request and begin implementation" -Color "Info"
Write-ColorOutput ""
Write-ColorOutput "Welcome to the Royaltea development team! 🚀" -Color "Success"
