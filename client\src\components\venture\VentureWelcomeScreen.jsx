import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, Card, CardBody } from '@heroui/react';
import { UserContext } from '../../../../contexts/supabase-auth.context';
import { useSupabase } from '../../hooks/useSupabase';

/**
 * VentureWelcomeScreen Component
 * 
 * Immersive welcome screen for venture creation
 * Shows alliance context and sets expectations for the process
 * Follows the wireframe design with full-screen layout
 */
const VentureWelcomeScreen = ({ 
  allianceId, 
  onStart, 
  onCancel 
}) => {
  const { currentUser } = useContext(UserContext);
  const { supabase } = useSupabase();
  const [allianceData, setAllianceData] = useState(null);
  const [memberCount, setMemberCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Load alliance data if provided
  useEffect(() => {
    if (allianceId) {
      loadAllianceData();
    } else {
      setIsLoading(false);
    }
  }, [allianceId]);

  const loadAllianceData = async () => {
    try {
      // Load alliance details
      const { data: alliance, error: allianceError } = await supabase
        .from('teams')
        .select('*')
        .eq('id', allianceId)
        .single();

      if (allianceError) throw allianceError;

      // Load member count
      const { data: members, error: membersError } = await supabase
        .from('team_members')
        .select('id')
        .eq('team_id', allianceId)
        .eq('status', 'active');

      if (membersError) throw membersError;

      setAllianceData(alliance);
      setMemberCount(members?.length || 0);
    } catch (error) {
      console.error('Error loading alliance data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <motion.div
      className="min-h-screen flex items-center justify-center p-4"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Exit button */}
      {onCancel && (
        <motion.div
          className="absolute top-6 right-6 z-10"
          variants={itemVariants}
        >
          <Button
            variant="light"
            size="lg"
            onPress={onCancel}
            isIconOnly
            className="text-white hover:bg-white/10"
          >
            <i className="bi bi-x-lg text-2xl"></i>
          </Button>
        </motion.div>
      )}

      <div className="max-w-2xl mx-auto text-center">
        {/* Main heading */}
        <motion.div variants={itemVariants} className="mb-8">
          <h1 className="text-6xl font-bold text-white mb-4">
            🚀 Create Your Venture
          </h1>
          <p className="text-xl text-white/80 leading-relaxed">
            Let's set up your project! We'll ask some simple questions to create 
            the perfect structure and automatically generate your collaboration agreement.
          </p>
        </motion.div>

        {/* Alliance context (if applicable) */}
        {allianceData && (
          <motion.div variants={itemVariants} className="mb-8">
            <Card className="bg-white/10 backdrop-blur-md border border-white/20">
              <CardBody className="p-6">
                <div className="flex items-center justify-center space-x-4 text-white">
                  <div className="text-4xl">👥</div>
                  <div className="text-left">
                    <h3 className="text-lg font-semibold">
                      Alliance: {allianceData.name}
                    </h3>
                    <p className="text-white/70">
                      {memberCount} {memberCount === 1 ? 'member' : 'members'} ready to contribute
                    </p>
                  </div>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        )}

        {/* Process overview */}
        <motion.div variants={itemVariants} className="mb-12">
          <div className="bg-white/10 backdrop-blur-md rounded-lg p-8 border border-white/20">
            <h2 className="text-2xl font-semibold text-white mb-6">
              What to Expect
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-white">
              <div className="text-center">
                <div className="text-3xl mb-3">❓</div>
                <h3 className="font-semibold mb-2">8 Simple Questions</h3>
                <p className="text-sm text-white/70">
                  Tell us about your project in plain language
                </p>
              </div>
              
              <div className="text-center">
                <div className="text-3xl mb-3">⚙️</div>
                <h3 className="font-semibold mb-2">Smart Configuration</h3>
                <p className="text-sm text-white/70">
                  We'll set up the technical details automatically
                </p>
              </div>
              
              <div className="text-center">
                <div className="text-3xl mb-3">📄</div>
                <h3 className="font-semibold mb-2">Legal Agreement</h3>
                <p className="text-sm text-white/70">
                  Professional contracts generated instantly
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Time estimate */}
        <motion.div variants={itemVariants} className="mb-8">
          <div className="flex items-center justify-center space-x-2 text-white/80">
            <i className="bi bi-clock text-lg"></i>
            <span className="text-lg">
              ● ● ● ● ● ● ● ●  (8 simple questions - about 5 minutes)
            </span>
          </div>
        </motion.div>

        {/* Start button */}
        <motion.div variants={itemVariants}>
          <Button
            size="lg"
            className="bg-white text-primary font-semibold px-12 py-4 text-lg hover:bg-white/90 transition-colors"
            onPress={onStart}
          >
            Start Now
          </Button>
        </motion.div>

        {/* Template shortcut hint */}
        <motion.div variants={itemVariants} className="mt-6">
          <p className="text-white/60 text-sm">
            💡 Power users: Template shortcuts available during setup
          </p>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default VentureWelcomeScreen;
