# Authentication Flow Wireframe
**Complete User Authentication Journey - IMMERSIVE PATTERN**

## 📋 Flow Information
- **Flow Type**: User Authentication
- **User Types**: All users (new and returning)
- **Entry Points**: Landing page, direct login link, expired session
- **Success Outcomes**: Authenticated user → Onboarding Flow (new) or Dashboard (returning)
- **Failure Outcomes**: Error states, account recovery
- **Pattern**: Full-screen immersive experience with minimal UI
- **Integration**: Connects directly to Onboarding Flow for new users

---

## 🔄 **Complete Authentication Flow**

```mermaid
flowchart TD
    A[Landing Page] --> B{User Action}
    B -->|Click Login| C[Login Page]
    B -->|Click Sign Up| D[Signup Page]
    B -->|Social Login| E[OAuth Provider]
    
    C --> F{Login Attempt}
    F -->|Valid Credentials| G[Dashboard]
    F -->|Invalid Credentials| H[Error State]
    F -->|Forgot Password| I[Password Reset]
    
    D --> J{Signup Attempt}
    J -->|Valid Data| K[Email Verification]
    J -->|Invalid Data| L[Validation Errors]
    J -->|Email Exists| M[Account Exists Error]
    
    E --> N{OAuth Response}
    N -->|Success| O[Profile Setup]
    N -->|Cancelled| A
    N -->|Error| P[OAuth Error]
    
    I --> Q[Reset Email Sent]
    Q --> R[Reset Link Clicked]
    R --> S[New Password Form]
    S --> T[Password Updated]
    T --> G
    
    K --> U[Verification Email]
    U --> V[Email Verified]
    V --> W[Onboarding Flow - NEW USER]
    W --> G

    O --> X{Profile Complete?}
    X -->|Yes| Y{First Time?}
    X -->|No| W

    Y -->|Yes| W
    Y -->|No| G
```

---

## 📱 **Page Wireframes**

### **1. Landing Page**
```
┌─────────────────────────────────────────────────────────────┐
│                        ROYALTEA                             │
│                 Fighting for the Tea                        │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│    Fair compensation for creative professionals             │
│                                                             │
│    ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│    │   [LOGIN]   │  │  [SIGN UP]  │  │ [LEARN MORE]│      │
│    └─────────────┘  └─────────────┘  └─────────────┘      │
│                                                             │
│    ┌─────────────┐  ┌─────────────┐                       │
│    │ [GitHub]    │  │ [Google]    │  Social Login         │
│    └─────────────┘  └─────────────┘                       │
│                                                             │
│    • Transparent revenue sharing                           │
│    • Fair contribution tracking                            │
│    • Professional collaboration tools                      │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### **2. Login Page - IMMERSIVE PATTERN**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✕                                                                           │
│                                                                             │
│                                                                             │
│                              Welcome Back                                  │
│                                                                             │
│                    ┌─────────────────────────────────┐                     │
│                    │                                 │                     │
│                    │         Email Address           │                     │
│                    │    [<EMAIL>]           │                     │
│                    │                                 │                     │
│                    │          Password               │                     │
│                    │    [••••••••••••]               │                     │
│                    │                                 │                     │
│                    │         [Sign In]               │                     │
│                    │                                 │                     │
│                    └─────────────────────────────────┘                     │
│                                                                             │
│                    ┌─────────────┐  ┌─────────────┐                       │
│                    │   Google    │  │   GitHub    │                       │
│                    └─────────────┘  └─────────────┘                       │
│                                                                             │
│                                                                             │
│                              [Join] ↓                                      │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **3. Signup Page - IMMERSIVE PATTERN**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ✕                                                                           │
│                                                                             │
│                                                                             │
│                            Create Your Account                             │
│                                                                             │
│                    ┌─────────────────────────────────┐                     │
│                    │                                 │                     │
│                    │          Username               │                     │
│                    │    [your-username]              │                     │
│                    │                                 │                     │
│                    │           Email                 │                     │
│                    │    [<EMAIL>]            │                     │
│                    │                                 │                     │
│                    │          Password               │                     │
│                    │    [••••••••••••]               │                     │
│                    │                                 │                     │
│                    │        Birth Date               │                     │
│                    │   [27] [January] [1981]         │                     │
│                    │                                 │                     │
│                    │         [Sign Up]               │                     │
│                    │                                 │                     │
│                    └─────────────────────────────────┘                     │
│                                                                             │
│                    ┌─────────────┐  ┌─────────────┐                       │
│                    │   Google    │  │   GitHub    │                       │
│                    └─────────────┘  └─────────────┘                       │
│                                                                             │
│                                                                             │
│                              [Login] ↓                                     │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **4. Password Reset Page**
```
┌─────────────────────────────────────────────────────────────┐
│  ← Back to Login                          ROYALTEA LOGO     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                    Reset Password                           │
│                                                             │
│    Enter your email address and we'll send you a link      │
│    to reset your password.                                  │
│                                                             │
│    Email Address                                            │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ <EMAIL>                                    │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    ┌─────────────────────────────────────────────────────┐ │
│    │                [SEND RESET LINK]                   │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    Remember your password? [Back to Login]                 │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

---

## 🎯 **Interaction Details**

### **Form Validation**
- **Real-time validation** on email format, password strength
- **Clear error messages** with specific guidance
- **Success indicators** for valid inputs
- **Accessibility** with proper ARIA labels

### **Loading States**
- **Button loading** with spinner during authentication
- **Page transitions** with loading indicators
- **Error recovery** with retry options

### **Error Handling**
- **Network errors** with retry functionality
- **Invalid credentials** with clear messaging
- **Account lockout** with recovery instructions
- **Server errors** with fallback options

### **Security Features**
- **Password strength** indicator
- **Rate limiting** protection
- **CSRF protection** on all forms
- **Secure session** management

---

## 📱 **Mobile Responsive Behavior**

### **Mobile Adaptations**
- **Single column** layout on mobile
- **Touch-friendly** button sizes (44px minimum)
- **Keyboard optimization** for form inputs
- **Biometric authentication** support where available

### **Progressive Enhancement**
- **Works without JavaScript** for basic functionality
- **Enhanced experience** with JavaScript enabled
- **Offline capability** for cached login

---

## 🔧 **Technical Requirements**

### **Authentication Methods**
- **Email/Password** with secure hashing
- **OAuth providers** (GitHub, Google)
- **Magic links** for passwordless login (future)
- **Two-factor authentication** (future)

### **Session Management**
- **JWT tokens** with refresh capability
- **Secure cookies** with proper flags
- **Session timeout** with warning
- **Multi-device** session management

### **Integration Points**
- **Supabase Auth** for backend authentication
- **User Context** for state management
- **Route protection** for authenticated areas
- **Analytics tracking** for conversion optimization

---

**This authentication flow serves as the foundation for all user interactions in the Royaltea platform. It must be implemented with the highest security standards and optimal user experience.**
