#!/bin/bash
# Remote Agent Setup Script
# Automated environment setup and validation for AI agents working on Royaltea platform

set -e  # Exit on any error

# Script configuration
AGENT_ID=""
SKIP_VALIDATION=false
VERBOSE=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --agent-id)
            AGENT_ID="$2"
            shift 2
            ;;
        --skip-validation)
            SKIP_VALIDATION=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [--agent-id ID] [--skip-validation] [--verbose]"
            echo "  --agent-id ID        Set agent identifier"
            echo "  --skip-validation    Skip final build validation"
            echo "  --verbose           Enable verbose output"
            exit 0
            ;;
        *)
            echo "Unknown option $1"
            exit 1
            ;;
    esac
done

# Helper functions
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_header() {
    local title=$1
    echo ""
    print_color $PURPLE "============================================================"
    print_color $PURPLE "  $title"
    print_color $PURPLE "============================================================"
    echo ""
}

print_success() {
    print_color $GREEN "✅ $1"
}

print_warning() {
    print_color $YELLOW "⚠️ $1"
}

print_error() {
    print_color $RED "❌ $1"
}

print_info() {
    print_color $CYAN "$1"
}

check_command() {
    local cmd=$1
    if command -v "$cmd" >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

test_file_exists() {
    local path=$1
    local description=$2
    if [[ -e "$path" ]]; then
        print_success "$description found: $path"
        return 0
    else
        print_error "$description missing: $path"
        return 1
    fi
}

get_agent_id() {
    if [[ -z "$AGENT_ID" ]]; then
        print_info "Please enter a unique Agent ID (e.g., 'agent-frontend-001'):"
        read -r AGENT_ID
        if [[ -z "$AGENT_ID" ]]; then
            print_error "Agent ID is required. Exiting."
            exit 1
        fi
    fi
    echo "$AGENT_ID"
}

# Main setup process
print_header "🤖 ROYALTEA REMOTE AGENT SETUP"

print_info "Setting up development environment for remote AI agent..."
print_info "Repository: Royaltea Platform (Design-Driven Development)"

# Get Agent ID
AGENT_ID=$(get_agent_id)
print_success "Agent ID: $AGENT_ID"

# Step 1: Validate repository structure
print_header "📁 REPOSITORY STRUCTURE VALIDATION"

declare -A required_paths=(
    ["docs/PRODUCT_REQUIREMENTS.md"]="Product Requirements Document"
    ["docs/design-system/README.md"]="Design System Documentation"
    ["docs/design-system/coding-instructions.md"]="Coding Instructions"
    ["docs/design-system/agent-quick-start.md"]="Agent Quick Start Guide"
    ["docs/design-system/agent-task-queue.md"]="Agent Task Queue"
    ["docs/wireframes/README.md"]="Wireframes Documentation"
    ["client/package.json"]="Frontend Package Configuration"
    ["client/src"]="Frontend Source Directory"
    ["netlify/functions"]="Backend Functions Directory"
)

validation_passed=true
for path in "${!required_paths[@]}"; do
    if ! test_file_exists "$path" "${required_paths[$path]}"; then
        validation_passed=false
    fi
done

if [[ "$validation_passed" != true ]]; then
    print_error "Repository structure validation failed. Please ensure you're in the correct Royaltea repository."
    exit 1
fi

print_success "Repository structure validated successfully"

# Step 2: Check required tools
print_header "🛠️ DEVELOPMENT TOOLS VALIDATION"

declare -A required_tools=(
    ["node"]="Node.js (required for frontend development)"
    ["npm"]="NPM (package manager)"
    ["git"]="Git (version control)"
)

tools_valid=true
for tool in "${!required_tools[@]}"; do
    if check_command "$tool"; then
        version=$($tool --version 2>/dev/null || echo "unknown")
        print_success "${required_tools[$tool]}: $version"
    else
        print_error "${required_tools[$tool]} not found"
        tools_valid=false
    fi
done

if [[ "$tools_valid" != true ]]; then
    print_error "Required tools missing. Please install missing tools and run setup again."
    exit 1
fi

# Step 3: Install dependencies
print_header "📦 DEPENDENCY INSTALLATION"

print_info "Installing frontend dependencies..."
cd client

if npm install; then
    print_success "Frontend dependencies installed successfully"
else
    print_error "Failed to install frontend dependencies"
    exit 1
fi

cd ..

# Step 4: Environment configuration
print_header "⚙️ ENVIRONMENT CONFIGURATION"

# Check for environment files
env_files=("client/.env.local" "client/.env.example")
for env_file in "${env_files[@]}"; do
    test_file_exists "$env_file" "Environment file" >/dev/null || true
done

# Create agent workspace
agent_workspace="agent-workspace/$AGENT_ID"
if [[ ! -d "$agent_workspace" ]]; then
    mkdir -p "$agent_workspace"
    print_success "Created agent workspace: $agent_workspace"
fi

# Step 5: Validate design system access
print_header "🎨 DESIGN SYSTEM VALIDATION"

declare -A design_system_files=(
    ["docs/design-system/bento-grid.md"]="Design Patterns"
    ["docs/design-system/colors.md"]="Color System"
    ["docs/design-system/typography.md"]="Typography System"
    ["docs/design-system/spacing.md"]="Spacing System"
    ["client/src/assets/design-system"]="Design Assets"
)

for file in "${!design_system_files[@]}"; do
    test_file_exists "$file" "${design_system_files[$file]}" >/dev/null || true
done

# Step 6: Create agent configuration
print_header "📋 AGENT CONFIGURATION"

config_path="$agent_workspace/agent-config.json"
cat > "$config_path" << EOF
{
  "agentId": "$AGENT_ID",
  "setupDate": "$(date '+%Y-%m-%d %H:%M:%S')",
  "repository": "Royaltea Platform",
  "workspaceDirectory": "$agent_workspace",
  "requiredReading": [
    "docs/design-system/agent-quick-start.md",
    "docs/PRODUCT_REQUIREMENTS.md",
    "docs/design-system/coding-instructions.md"
  ],
  "availableTasks": "docs/design-system/agent-task-queue.md",
  "reportingFormat": "docs/design-system/remote-agent-setup.md#progress-reporting-system"
}
EOF

print_success "Agent configuration saved: $config_path"

# Step 7: Generate quick reference
quick_ref_path="$agent_workspace/quick-reference.md"
cat > "$quick_ref_path" << EOF
# $AGENT_ID Quick Reference
Generated: $(date '+%Y-%m-%d %H:%M:%S')

## Essential Reading (Complete in order):
1. docs/design-system/agent-quick-start.md (15 minutes)
2. docs/PRODUCT_REQUIREMENTS.md (5 minutes)
3. docs/design-system/coding-instructions.md (10 minutes)

## Key Requirements:
- Design Fidelity: 95%+ match to wireframes
- Performance: <5 minutes to first meaningful action
- Test Coverage: >90%
- Accessibility: WCAG 2.1 AA compliance

## Design Patterns:
- Bento Grid: Dashboards/management (3-column layout)
- Immersive Flow: Onboarding/creation (full-screen, minimal UI)

## Available Tasks:
See: docs/design-system/agent-task-queue.md

## Daily Reporting:
Required format in: docs/design-system/remote-agent-setup.md

## Emergency Escalation:
- Technical Issues: Report in daily status
- Design Questions: Reference design documentation
- Urgent Blockers: Mark status as "URGENT"

## Workspace Directory:
$agent_workspace

## Next Steps:
1. Read essential documentation
2. Select task from queue
3. Submit task request
4. Begin implementation following 5-phase process
EOF

print_success "Quick reference guide created: $quick_ref_path"

# Step 8: Final validation
if [[ "$SKIP_VALIDATION" != true ]]; then
    print_header "🔍 FINAL VALIDATION"
    
    # Test build process
    print_info "Testing build process..."
    cd client
    
    if npm run build >/dev/null 2>&1; then
        print_success "Build process validated successfully"
    else
        print_warning "Build validation failed - may need environment configuration"
    fi
    
    cd ..
fi

# Setup complete
print_header "🎉 SETUP COMPLETE"

print_success "Remote Agent setup completed successfully!"
echo ""
print_info "Agent ID: $AGENT_ID"
print_info "Workspace: $agent_workspace"
print_info "Configuration: $config_path"
print_info "Quick Reference: $quick_ref_path"
echo ""
print_color $PURPLE "NEXT STEPS:"
print_info "1. Read the quick reference guide: $quick_ref_path"
print_info "2. Complete essential reading (30 minutes total)"
print_info "3. Select a task from: docs/design-system/agent-task-queue.md"
print_info "4. Submit task request and begin implementation"
echo ""
print_success "Welcome to the Royaltea development team! 🚀"
