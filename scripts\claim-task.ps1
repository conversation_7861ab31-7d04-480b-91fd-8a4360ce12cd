# Task Claiming Helper Script (PowerShell)
# Helps agents claim tasks and prevent conflicts

param(
    [string]$AgentId = "",
    [string]$TaskId = "",
    [switch]$Help = $false
)

# Colors for output
$Colors = @{
    Success = "Green"
    Warning = "Yellow" 
    Error = "Red"
    Info = "Cyan"
}

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Colors[$Color]
}

function Show-Help {
    Write-Host "Task Claiming Helper Script"
    Write-Host ""
    Write-Host "Usage: .\claim-task.ps1 -AgentId AGENT_ID -TaskId TASK_ID"
    Write-Host ""
    Write-Host "Parameters:"
    Write-Host "  -AgentId ID    Your unique agent identifier"
    Write-Host "  -TaskId ID     Task to claim (e.g., A1, B2, C3)"
    Write-Host "  -Help          Show this help message"
    Write-Host ""
    Write-Host "Example: .\claim-task.ps1 -AgentId 'agent-001' -TaskId 'A1'"
    exit 0
}

if ($Help) {
    Show-Help
}

# Validate inputs
if ($AgentId -eq "") {
    Write-ColorOutput "❌ Agent ID is required. Use -AgentId YOUR_ID" -Color "Error"
    exit 1
}

if ($TaskId -eq "") {
    Write-ColorOutput "❌ Task ID is required. Use -TaskId TASK_ID" -Color "Error"
    exit 1
}

# Configuration
$TaskStatusFile = "agent-workspace/shared/task-status.md"

# Check if task status file exists
if (-not (Test-Path $TaskStatusFile)) {
    Write-ColorOutput "❌ Task status file not found: $TaskStatusFile" -Color "Error"
    Write-ColorOutput "Please ensure you're in the repository root and the shared workspace exists" -Color "Info"
    exit 1
}

Write-ColorOutput "🤖 Task Claiming Helper" -Color "Info"
Write-ColorOutput "Agent ID: $AgentId" -Color "Info"
Write-ColorOutput "Task ID: $TaskId" -Color "Info"
Write-Host ""

# Check current task status
Write-ColorOutput "Checking current task status..." -Color "Info"

$TaskContent = Get-Content $TaskStatusFile -Raw
if ($TaskContent -match "Task $TaskId:") {
    # Extract current status using regex
    $TaskSection = ($TaskContent -split "Task $TaskId:")[1] -split "###")[0]
    
    if ($TaskSection -match "- \*\*Status\*\*: (.+)") {
        $CurrentStatus = $matches[1]
        Write-ColorOutput "Found task: Status: $CurrentStatus" -Color "Info"
    }
    
    if ($TaskSection -match "- \*\*Claimed By\*\*: (.+)") {
        $ClaimedBy = $matches[1]
        Write-ColorOutput "Current claim: Claimed By: $ClaimedBy" -Color "Info"
        
        # Check if already claimed
        if ($ClaimedBy -eq "None") {
            Write-ColorOutput "✅ Task is available for claiming" -Color "Success"
        }
        elseif ($ClaimedBy -eq $AgentId) {
            Write-ColorOutput "⚠️ Task already claimed by you ($AgentId)" -Color "Warning"
            Write-ColorOutput "No action needed" -Color "Info"
            exit 0
        }
        else {
            Write-ColorOutput "❌ Task already claimed by: $ClaimedBy" -Color "Error"
            Write-ColorOutput "Please choose a different task or coordinate with the other agent" -Color "Info"
            exit 1
        }
    }
}
else {
    Write-ColorOutput "❌ Task $TaskId not found in status file" -Color "Error"
    Write-ColorOutput "Available tasks:" -Color "Info"
    $AvailableTasks = Select-String -Path $TaskStatusFile -Pattern "Task [A-Z][0-9]:" | Select-Object -First 10
    $AvailableTasks | ForEach-Object { Write-Host $_.Line }
    exit 1
}

# Create backup
$BackupFile = "$TaskStatusFile.backup"
Copy-Item $TaskStatusFile $BackupFile
Write-ColorOutput "Created backup: $BackupFile" -Color "Info"

# Claim the task
Write-ColorOutput "Claiming task $TaskId for agent $AgentId..." -Color "Info"

$Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

# Read the file content
$Content = Get-Content $TaskStatusFile

# Find and update the task section
$InTaskSection = $false
$TaskFound = $false
$UpdatedContent = @()

foreach ($Line in $Content) {
    if ($Line -match "Task $TaskId:") {
        $InTaskSection = $true
        $TaskFound = $true
        $UpdatedContent += $Line
    }
    elseif ($InTaskSection -and $Line -match "^### ") {
        $InTaskSection = $false
        $UpdatedContent += $Line
    }
    elseif ($InTaskSection) {
        if ($Line -match "- \*\*Status\*\*:") {
            $UpdatedContent += "- **Status**: 🔄 IN PROGRESS"
        }
        elseif ($Line -match "- \*\*Claimed By\*\*:") {
            $UpdatedContent += "- **Claimed By**: $AgentId"
        }
        elseif ($Line -match "- \*\*Claim Time\*\*:") {
            $UpdatedContent += "- **Claim Time**: $Timestamp"
        }
        elseif ($Line -match "- \*\*Available\*\*:") {
            $UpdatedContent += "- **Last Update**: $Timestamp - `"Task claimed by $AgentId`""
            $UpdatedContent += "- **Available**: No"
        }
        elseif ($Line -match "- \*\*Last Update\*\*:") {
            # Skip old last update line
        }
        else {
            $UpdatedContent += $Line
        }
    }
    else {
        $UpdatedContent += $Line
    }
}

if (-not $TaskFound) {
    Write-ColorOutput "❌ Failed to find and update task $TaskId" -Color "Error"
    exit 1
}

# Write the updated content back to the file
$UpdatedContent | Out-File -FilePath $TaskStatusFile -Encoding UTF8

Write-ColorOutput "✅ Task $TaskId claimed successfully!" -Color "Success"

# Show the updated section
Write-ColorOutput "Updated task status:" -Color "Info"
Write-Host ""
$TaskSection = Select-String -Path $TaskStatusFile -Pattern "Task $TaskId:" -Context 0,10
$TaskSection.Line
$TaskSection.Context.PostContext | Select-Object -First 10

Write-Host ""
Write-ColorOutput "Next steps:" -Color "Info"
Write-ColorOutput "1. Commit this change: git add $TaskStatusFile && git commit -m 'Claim Task $TaskId`: $AgentId'" -Color "Info"
Write-ColorOutput "2. Wait 5 minutes for other agents to see the claim" -Color "Info"
Write-ColorOutput "3. Begin implementation following the 5-phase process" -Color "Info"
Write-ColorOutput "4. Update progress every 4 hours in the shared status file" -Color "Info"

Write-Host ""
Write-ColorOutput "✅ Task claiming complete! Good luck with your implementation! 🚀" -Color "Success"
