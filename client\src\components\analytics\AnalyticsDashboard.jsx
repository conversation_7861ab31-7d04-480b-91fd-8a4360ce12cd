import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Progress, Select, SelectItem, Tabs, Tab } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../../contexts/supabase-auth.context';
import { toast } from 'react-hot-toast';

// Import analytics components
import RevenueMetrics from './RevenueMetrics';
import GrowthTrends from './GrowthTrends';
import PerformanceScore from './PerformanceScore';
import SuccessRate from './SuccessRate';
import DetailedBreakdown from './DetailedBreakdown';
import TopPerformers from './TopPerformers';
import AIInsights from './AIInsights';
import QuickActions from './QuickActions';

/**
 * Analytics Dashboard - Comprehensive data visualization and insights
 * 
 * Features:
 * - Advanced bento grid layout with varied widget sizes
 * - Real-time performance metrics and KPIs
 * - Interactive visualizations with drill-down capabilities
 * - Predictive analytics and AI-powered insights
 * - Custom report generation and export functionality
 * - Role-based data visibility and access control
 */
const AnalyticsDashboard = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  
  // State management
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [analyticsData, setAnalyticsData] = useState({
    revenue: {
      total: 47200,
      thisMonth: 18400,
      growth: 23,
      platformFees: 2600,
      avgMonthly: 2650,
      hourlyRate: 47.50
    },
    performance: {
      score: 85,
      successRate: 94,
      completedMissions: 89,
      avgCompletionTime: 8.2,
      qualityScore: 4.7
    },
    trends: {
      revenueGrowth: [20000, 25000, 32000, 38000, 42000, 47200],
      userGrowth: [120, 135, 148, 152, 156, 162],
      missionGrowth: [65, 72, 78, 83, 87, 89]
    }
  });

  // Time period options
  const timePeriods = [
    { key: '7d', label: 'Last 7 Days' },
    { key: '30d', label: 'Last 30 Days' },
    { key: '90d', label: 'Last 90 Days' },
    { key: '6m', label: 'Last 6 Months' },
    { key: '1y', label: 'Last Year' }
  ];

  // Load analytics data
  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      
      // Simulate API call with mock data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data based on selected period
      const mockData = {
        revenue: {
          total: selectedPeriod === '7d' ? 12000 : selectedPeriod === '30d' ? 47200 : 142000,
          thisMonth: selectedPeriod === '7d' ? 12000 : 18400,
          growth: selectedPeriod === '7d' ? 15 : selectedPeriod === '30d' ? 23 : 28,
          platformFees: selectedPeriod === '7d' ? 720 : 2600,
          avgMonthly: 2650,
          hourlyRate: 47.50
        },
        performance: {
          score: 85,
          successRate: selectedPeriod === '7d' ? 96 : 94,
          completedMissions: selectedPeriod === '7d' ? 23 : 89,
          avgCompletionTime: 8.2,
          qualityScore: 4.7
        },
        trends: {
          revenueGrowth: selectedPeriod === '7d' 
            ? [8000, 9200, 10500, 11200, 11800, 12000]
            : [20000, 25000, 32000, 38000, 42000, 47200],
          userGrowth: selectedPeriod === '7d'
            ? [156, 158, 159, 160, 161, 162]
            : [120, 135, 148, 152, 156, 162],
          missionGrowth: selectedPeriod === '7d'
            ? [84, 85, 86, 87, 88, 89]
            : [65, 72, 78, 83, 87, 89]
        }
      };
      
      setAnalyticsData(mockData);
      
    } catch (error) {
      console.error('Error loading analytics data:', error);
      toast.error('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  // Handle period change
  const handlePeriodChange = (period) => {
    setSelectedPeriod(period);
  };

  // Initialize component
  useEffect(() => {
    loadAnalyticsData();
  }, [selectedPeriod]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-default-600">Loading analytics data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`analytics-dashboard ${className}`}>
      {/* Dashboard Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
              📊 Analytics Command Center
            </h1>
            <p className="text-default-600">
              Comprehensive performance insights and data-driven decision making
            </p>
          </div>
          
          {/* Time Period Selector */}
          <div className="flex items-center gap-4">
            <Select
              selectedKeys={[selectedPeriod]}
              onSelectionChange={(keys) => handlePeriodChange(Array.from(keys)[0])}
              className="w-40"
              size="sm"
            >
              {timePeriods.map(period => (
                <SelectItem key={period.key}>{period.label}</SelectItem>
              ))}
            </Select>
            <Button color="primary" variant="flat" size="sm">
              📤 Export
            </Button>
          </div>
        </div>
      </div>

      {/* Top Row - Core Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-6">
        {/* Revenue Metrics - 2x2 widget */}
        <motion.div
          className="md:col-span-2"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <RevenueMetrics 
            data={analyticsData.revenue}
            period={selectedPeriod}
          />
        </motion.div>

        {/* Performance Score - 1x1 widget */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <PerformanceScore 
            score={analyticsData.performance.score}
            period={selectedPeriod}
          />
        </motion.div>

        {/* Success Rate - 1x1 widget */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <SuccessRate 
            data={analyticsData.performance}
            period={selectedPeriod}
          />
        </motion.div>
      </div>

      {/* Growth Trends - 2x2 widget */}
      <motion.div
        className="mb-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.4 }}
      >
        <GrowthTrends 
          data={analyticsData.trends}
          period={selectedPeriod}
        />
      </motion.div>

      {/* Detailed Breakdown - 6x2 widget */}
      <motion.div
        className="mb-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.5 }}
      >
        <DetailedBreakdown 
          data={analyticsData}
          period={selectedPeriod}
        />
      </motion.div>

      {/* Bottom Row - Insights and Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Top Performers - 2x1 widget */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.6 }}
        >
          <TopPerformers 
            period={selectedPeriod}
          />
        </motion.div>

        {/* AI Insights - 2x1 widget */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.7 }}
        >
          <AIInsights 
            data={analyticsData}
            period={selectedPeriod}
          />
        </motion.div>

        {/* Quick Actions - 2x1 widget */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.8 }}
        >
          <QuickActions 
            onExport={(format) => toast.success(`Exporting data as ${format}`)}
            onCreateReport={() => toast.success('Opening report builder')}
            onSetAlert={() => toast.success('Alert configured')}
          />
        </motion.div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
