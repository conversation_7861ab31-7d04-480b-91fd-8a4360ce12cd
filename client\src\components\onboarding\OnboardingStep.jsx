import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, Button } from '@heroui/react';

/**
 * OnboardingStep Component
 * 
 * Individual step template for onboarding flow
 * Reusable component for different step types
 */
const OnboardingStep = ({
  title,
  subtitle,
  type = 'selection', // 'welcome', 'selection', 'form', 'success'
  options = [],
  onOptionSelect,
  onContinue,
  onBack,
  showBack = false,
  showSkip = false,
  onSkip,
  children,
  className = ""
}) => {
  const renderWelcomeStep = () => (
    <>
      <motion.h1
        className="text-4xl md:text-6xl font-bold text-white mb-4"
        initial={{ scale: 0.9 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.2 }}
      >
        {title}
      </motion.h1>

      {subtitle && (
        <motion.p
          className="text-xl md:text-2xl text-white text-opacity-80 mb-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
        >
          {subtitle}
        </motion.p>
      )}

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.6 }}
      >
        <Button
          onClick={onContinue}
          size="lg"
          className="bg-white bg-opacity-20 backdrop-blur-md text-white border border-white border-opacity-30 px-8 py-3 text-lg"
        >
          Continue
        </Button>
      </motion.div>
    </>
  );

  const renderSelectionStep = () => (
    <>
      <motion.h1
        className="text-3xl md:text-5xl font-bold text-white mb-8"
        initial={{ scale: 0.9 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.2 }}
      >
        {title}
      </motion.h1>

      {/* Options Grid */}
      <motion.div
        className={`grid gap-6 mb-8 ${
          options.length === 2 
            ? 'grid-cols-1 md:grid-cols-2 max-w-4xl mx-auto' 
            : 'grid-cols-1 md:grid-cols-3'
        }`}
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        {options.map((option, index) => (
          <motion.div
            key={option.id}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 + index * 0.1 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Card
              className="cursor-pointer bg-white bg-opacity-10 backdrop-blur-md border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300"
              isPressable
              onPress={() => onOptionSelect(option)}
            >
              <CardBody className="flex flex-col items-center justify-center text-white p-8 min-h-[200px]">
                <div className="text-5xl mb-4">{option.icon}</div>
                <h3 className="text-xl font-bold mb-2 text-center">{option.title}</h3>
                {option.description && (
                  <p className="text-sm opacity-90 text-center">{option.description}</p>
                )}
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      {/* Skip Option */}
      {showSkip && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
          className="mb-8"
        >
          <Button
            onClick={onSkip}
            variant="ghost"
            className="text-white text-opacity-70 hover:text-opacity-100 bg-transparent hover:bg-white hover:bg-opacity-10"
          >
            Skip Setup
          </Button>
        </motion.div>
      )}

      {/* Back Button */}
      {showBack && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
        >
          <Button
            onClick={onBack}
            variant="ghost"
            className="text-white text-opacity-70 hover:text-opacity-100 bg-transparent hover:bg-white hover:bg-opacity-10"
          >
            Back
          </Button>
        </motion.div>
      )}
    </>
  );

  const renderFormStep = () => (
    <>
      <motion.h1
        className="text-3xl md:text-5xl font-bold text-white mb-8"
        initial={{ scale: 0.9 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.2 }}
      >
        {title}
      </motion.h1>

      <motion.div
        className="bg-white bg-opacity-10 backdrop-blur-md rounded-lg p-8 border border-white border-opacity-20 max-w-2xl mx-auto"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        {children}
      </motion.div>

      {/* Navigation Buttons */}
      <motion.div
        className="flex gap-4 justify-center mt-8"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.6 }}
      >
        {showBack && (
          <Button
            onClick={onBack}
            variant="ghost"
            className="text-white text-opacity-70 hover:text-opacity-100 bg-transparent hover:bg-white hover:bg-opacity-10 px-6 py-3"
            size="lg"
          >
            Back
          </Button>
        )}
        
        <Button
          onClick={onContinue}
          size="lg"
          className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-8 py-3 text-lg font-medium"
        >
          Continue
        </Button>
      </motion.div>
    </>
  );

  const renderCustomStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full"
    >
      {children}
    </motion.div>
  );

  return (
    <div className={`text-center max-w-4xl mx-auto px-8 ${className}`}>
      {type === 'welcome' && renderWelcomeStep()}
      {type === 'selection' && renderSelectionStep()}
      {type === 'form' && renderFormStep()}
      {type === 'custom' && renderCustomStep()}
    </div>
  );
};

export default OnboardingStep;
