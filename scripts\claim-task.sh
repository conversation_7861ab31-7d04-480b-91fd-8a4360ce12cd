#!/bin/bash
# Task Claiming Helper Script
# Helps agents claim tasks and prevent conflicts

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Helper functions
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_success() {
    print_color $GREEN "✅ $1"
}

print_warning() {
    print_color $YELLOW "⚠️ $1"
}

print_error() {
    print_color $RED "❌ $1"
}

print_info() {
    print_color $CYAN "$1"
}

# Configuration
TASK_STATUS_FILE="agent-workspace/shared/task-status.md"
AGENT_ID=""
TASK_ID=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --agent-id)
            AGENT_ID="$2"
            shift 2
            ;;
        --task-id)
            TASK_ID="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 --agent-id AGENT_ID --task-id TASK_ID"
            echo "  --agent-id ID    Your unique agent identifier"
            echo "  --task-id ID     Task to claim (e.g., A1, B2, C3)"
            echo ""
            echo "Example: $0 --agent-id agent-001 --task-id A1"
            exit 0
            ;;
        *)
            echo "Unknown option $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Validate inputs
if [[ -z "$AGENT_ID" ]]; then
    print_error "Agent ID is required. Use --agent-id YOUR_ID"
    exit 1
fi

if [[ -z "$TASK_ID" ]]; then
    print_error "Task ID is required. Use --task-id TASK_ID"
    exit 1
fi

# Check if task status file exists
if [[ ! -f "$TASK_STATUS_FILE" ]]; then
    print_error "Task status file not found: $TASK_STATUS_FILE"
    print_info "Please ensure you're in the repository root and the shared workspace exists"
    exit 1
fi

print_info "🤖 Task Claiming Helper"
print_info "Agent ID: $AGENT_ID"
print_info "Task ID: $TASK_ID"
echo ""

# Check current task status
print_info "Checking current task status..."

if grep -q "Task $TASK_ID:" "$TASK_STATUS_FILE"; then
    # Extract current status
    CURRENT_STATUS=$(grep -A 5 "Task $TASK_ID:" "$TASK_STATUS_FILE" | grep "Status:" | head -1)
    CURRENT_CLAIMED_BY=$(grep -A 5 "Task $TASK_ID:" "$TASK_STATUS_FILE" | grep "Claimed By:" | head -1)
    
    print_info "Found task: $CURRENT_STATUS"
    print_info "Current claim: $CURRENT_CLAIMED_BY"
    
    # Check if already claimed
    if echo "$CURRENT_CLAIMED_BY" | grep -q "None"; then
        print_success "Task is available for claiming"
    else
        CLAIMED_AGENT=$(echo "$CURRENT_CLAIMED_BY" | sed 's/.*Claimed By: //' | sed 's/\*\*//')
        if [[ "$CLAIMED_AGENT" == "$AGENT_ID" ]]; then
            print_warning "Task already claimed by you ($AGENT_ID)"
            print_info "No action needed"
            exit 0
        else
            print_error "Task already claimed by: $CLAIMED_AGENT"
            print_info "Please choose a different task or coordinate with the other agent"
            exit 1
        fi
    fi
else
    print_error "Task $TASK_ID not found in status file"
    print_info "Available tasks:"
    grep "Task [A-Z][0-9]:" "$TASK_STATUS_FILE" | head -10
    exit 1
fi

# Create backup
cp "$TASK_STATUS_FILE" "${TASK_STATUS_FILE}.backup"
print_info "Created backup: ${TASK_STATUS_FILE}.backup"

# Claim the task
print_info "Claiming task $TASK_ID for agent $AGENT_ID..."

TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# Use sed to update the task status
sed -i.tmp "/Task $TASK_ID:/,/^### / {
    s/- \*\*Status\*\*: .*/- **Status**: 🔄 IN PROGRESS/
    s/- \*\*Claimed By\*\*: .*/- **Claimed By**: $AGENT_ID/
    s/- \*\*Claim Time\*\*: .*/- **Claim Time**: $TIMESTAMP/
    /- \*\*Last Update\*\*/d
    /- \*\*Available\*\*/c\\
- **Last Update**: $TIMESTAMP - \"Task claimed by $AGENT_ID\"\\
- **Available**: No
}" "$TASK_STATUS_FILE"

# Remove temporary file
rm -f "${TASK_STATUS_FILE}.tmp"

print_success "Task $TASK_ID claimed successfully!"

# Show the updated section
print_info "Updated task status:"
echo ""
grep -A 10 "Task $TASK_ID:" "$TASK_STATUS_FILE" | head -15

echo ""
print_info "Next steps:"
print_info "1. Commit this change: git add $TASK_STATUS_FILE && git commit -m 'Claim Task $TASK_ID: $AGENT_ID'"
print_info "2. Wait 5 minutes for other agents to see the claim"
print_info "3. Begin implementation following the 5-phase process"
print_info "4. Update progress every 4 hours in the shared status file"

echo ""
print_success "Task claiming complete! Good luck with your implementation! 🚀"
