@echo off
REM Remote Agent Setup Launcher
REM Cross-platform launcher for agent setup scripts

echo.
echo ========================================================
echo   ROYALTEA REMOTE AGENT SETUP LAUNCHER
echo ========================================================
echo.

REM Detect operating system and run appropriate script
if exist "%SystemRoot%\System32\WindowsPowerShell\v1.0\powershell.exe" (
    echo Detected Windows - Running PowerShell setup script...
    echo.
    powershell -ExecutionPolicy Bypass -File "%~dp0setup-remote-agent.ps1" %*
) else (
    echo Detected Unix/Linux - Running Bash setup script...
    echo.
    bash "%~dp0setup-remote-agent.sh" %*
)

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Setup failed with error code %ERRORLEVEL%
    echo Please check the output above for details.
    pause
    exit /b %ERRORLEVEL%
)

echo.
echo Setup completed successfully!
pause
