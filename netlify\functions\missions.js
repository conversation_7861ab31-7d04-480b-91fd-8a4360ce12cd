// Mission & Bounty Management API
// Backend Specialist: Core mission/task CRUD operations
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Get missions for a user or venture
const getMissions = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const queryParams = new URLSearchParams(event.queryStringParameters || {});
    const ventureId = queryParams.get('venture_id');
    const category = queryParams.get('category'); // 'mission', 'bounty', 'all'
    const status = queryParams.get('status');
    const isPublic = queryParams.get('public') === 'true';

    let query = supabase
      .from('tasks')
      .select(`
        *,
        projects(
          id,
          name,
          title,
          team_id,
          teams(
            id,
            name,
            team_members(user_id, role)
          )
        ),
        assignee:users!tasks_assignee_id_fkey(
          id,
          display_name,
          avatar_url
        ),
        creator:users!tasks_created_by_fkey(
          id,
          display_name,
          avatar_url
        )
      `);

    // Filter by venture if specified
    if (ventureId) {
      query = query.eq('project_id', ventureId);
    } else {
      // Get missions for user's ventures or assigned to user
      query = query.or(`assignee_id.eq.${userId},project_id.in.(select id from projects where created_by = '${userId}' or team_id in (select team_id from team_members where user_id = '${userId}'))`);
    }

    // Filter by category (using task_type as proxy for now)
    if (category && category !== 'all') {
      if (category === 'bounty') {
        // For now, treat certain task types as bounties
        query = query.in('task_type', ['bounty', 'freelance', 'contract']);
      } else if (category === 'mission') {
        query = query.not('task_type', 'in', '(bounty,freelance,contract)');
      }
    }

    // Filter by status
    if (status) {
      query = query.eq('status', status);
    }

    // Filter by public visibility (using project's is_public for now)
    if (isPublic) {
      query = query.eq('projects.is_public', true);
    }

    const { data: missions, error } = await query
      .order('created_at', { ascending: false })
      .limit(50);

    if (error) throw error;

    // Transform tasks to missions format
    const missionsResponse = missions.map(task => {
      const hasAccess = task.assignee_id === userId ||
        task.creator?.id === userId ||
        (task.projects?.teams?.team_members?.some(m => m.user_id === userId));

      return {
        id: task.id,
        title: task.title,
        description: task.description,
        category: task.task_type === 'bounty' || task.task_type === 'freelance' ? 'bounty' : 'mission',
        status: task.status,
        priority: 'medium', // Default since priority column may not exist yet
        difficulty_level: task.difficulty_level,
        difficulty_points: task.difficulty_points,
        estimated_hours: task.estimated_hours,
        logged_hours: task.logged_hours,
        bounty_amount: 0, // Default since bounty_amount column may not exist yet
        bounty_currency: 'USD',
        is_public: task.projects?.is_public || false,
        deadline: null, // Default since deadline column may not exist yet
        venture: task.projects ? {
          id: task.projects.id,
          name: task.projects.name || task.projects.title,
          alliance: task.projects.teams ? {
            id: task.projects.teams.id,
            name: task.projects.teams.name
          } : null
        } : null,
        assignee: task.assignee,
        creator: task.creator,
        created_at: task.created_at,
        updated_at: task.updated_at,
        completed_at: task.completed_at,
        has_access: hasAccess
      };
    });

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ missions: missionsResponse })
    };

  } catch (error) {
    console.error('Get missions error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch missions' })
    };
  }
};

// Get single mission details
const getMission = async (event) => {
  try {
    const missionId = event.path.split('/').pop();
    const userId = getUserFromRequest(event);
    
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Get mission with full details
    const { data: mission, error: missionError } = await supabase
      .from('tasks')
      .select(`
        *,
        projects(
          id,
          name,
          title,
          is_public,
          team_id,
          teams(
            id,
            name,
            team_members(user_id, role)
          )
        ),
        assignee:users!tasks_assignee_id_fkey(
          id,
          display_name,
          email,
          avatar_url
        ),
        creator:users!tasks_created_by_fkey(
          id,
          display_name,
          avatar_url
        )
      `)
      .eq('id', missionId)
      .single();

    if (missionError) throw missionError;

    // Check if user has access
    const hasAccess = mission.assignee_id === userId ||
      mission.creator?.id === userId ||
      (mission.projects?.teams?.team_members?.some(m => m.user_id === userId)) ||
      mission.projects?.is_public;

    if (!hasAccess) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Access denied' })
      };
    }

    // Transform response
    const response = {
      id: mission.id,
      title: mission.title,
      description: mission.description,
      category: mission.task_type === 'bounty' || mission.task_type === 'freelance' ? 'bounty' : 'mission',
      status: mission.status,
      priority: 'medium',
      difficulty_level: mission.difficulty_level,
      difficulty_points: mission.difficulty_points,
      estimated_hours: mission.estimated_hours,
      logged_hours: mission.logged_hours,
      bounty_amount: 0,
      bounty_currency: 'USD',
      is_public: mission.projects?.is_public || false,
      deadline: null,
      required_skills: [],
      venture: mission.projects ? {
        id: mission.projects.id,
        name: mission.projects.name || mission.projects.title,
        alliance: mission.projects.teams ? {
          id: mission.projects.teams.id,
          name: mission.projects.teams.name,
          members: mission.projects.teams.team_members
        } : null
      } : null,
      assignee: mission.assignee,
      creator: mission.creator,
      created_at: mission.created_at,
      updated_at: mission.updated_at,
      completed_at: mission.completed_at,
      external_tool_info: {
        source_tool: mission.source_tool,
        external_tool_id: mission.external_tool_id,
        external_tool_link: mission.external_tool_link
      }
    };

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(response)
    };

  } catch (error) {
    console.error('Get mission error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch mission' })
    };
  }
};

// Create new mission
const createMission = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const data = JSON.parse(event.body);
    
    // Validate required fields
    if (!data.title || !data.description || !data.project_id) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Title, description, and project_id are required' })
      };
    }

    // Check if user has permission to create missions in this venture
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select(`
        id,
        created_by,
        team_id,
        teams(
          team_members(user_id, role)
        )
      `)
      .eq('id', data.project_id)
      .single();

    if (projectError) throw projectError;

    const hasPermission = project.created_by === userId ||
      (project.teams && project.teams.team_members.some(m => 
        m.user_id === userId && ['founder', 'owner', 'admin', 'member'].includes(m.role)
      ));

    if (!hasPermission) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'No permission to create missions in this venture' })
      };
    }

    // Create mission (task)
    const missionData = {
      project_id: data.project_id,
      title: data.title,
      description: data.description,
      status: data.status || 'todo',
      task_type: data.category === 'bounty' ? 'bounty' : (data.task_type || 'development'),
      difficulty_level: data.difficulty_level || 'medium',
      difficulty_points: data.difficulty_points || 2,
      estimated_hours: data.estimated_hours || 0,
      assignee_id: data.assignee_id || null,
      source_tool: 'internal'
    };

    const { data: mission, error: missionError } = await supabase
      .from('tasks')
      .insert([missionData])
      .select()
      .single();

    if (missionError) throw missionError;

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        mission: {
          id: mission.id,
          title: mission.title,
          description: mission.description,
          category: mission.task_type === 'bounty' ? 'bounty' : 'mission',
          status: mission.status,
          project_id: mission.project_id,
          created_at: mission.created_at
        }
      })
    };

  } catch (error) {
    console.error('Create mission error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to create mission' })
    };
  }
};

// Update mission
const updateMission = async (event) => {
  try {
    const missionId = event.path.split('/').pop();
    const userId = getUserFromRequest(event);
    
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Check if user has permission to update
    const { data: mission, error: missionError } = await supabase
      .from('tasks')
      .select(`
        *,
        projects(
          created_by,
          teams(
            team_members(user_id, role)
          )
        )
      `)
      .eq('id', missionId)
      .single();

    if (missionError) throw missionError;

    const hasPermission = mission.assignee_id === userId ||
      mission.projects.created_by === userId ||
      (mission.projects.teams && mission.projects.teams.team_members.some(m => 
        m.user_id === userId && ['founder', 'owner', 'admin'].includes(m.role)
      ));

    if (!hasPermission) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Insufficient permissions' })
      };
    }

    const data = JSON.parse(event.body);
    
    // Update mission
    const updateData = {};
    if (data.title) updateData.title = data.title;
    if (data.description) updateData.description = data.description;
    if (data.status) updateData.status = data.status;
    if (data.difficulty_level) updateData.difficulty_level = data.difficulty_level;
    if (data.difficulty_points) updateData.difficulty_points = data.difficulty_points;
    if (data.estimated_hours) updateData.estimated_hours = data.estimated_hours;
    if (data.logged_hours) updateData.logged_hours = data.logged_hours;
    if (data.assignee_id !== undefined) updateData.assignee_id = data.assignee_id;
    if (data.category) updateData.task_type = data.category === 'bounty' ? 'bounty' : updateData.task_type;
    
    updateData.updated_at = new Date().toISOString();
    
    if (data.status === 'done' && !mission.completed_at) {
      updateData.completed_at = new Date().toISOString();
    }

    const { data: updatedMission, error: updateError } = await supabase
      .from('tasks')
      .update(updateData)
      .eq('id', missionId)
      .select()
      .single();

    if (updateError) throw updateError;

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ mission: updatedMission })
    };

  } catch (error) {
    console.error('Update mission error:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to update mission' })
    };
  }
};

// Route requests to appropriate handlers
exports.handler = async (event, context) => {
  // Add CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: ''
    };
  }

  const path = event.path.replace('/.netlify/functions/missions', '');

  try {
    let response;

    if (event.httpMethod === 'GET') {
      if (path === '' || path === '/') {
        response = await getMissions(event);
      } else {
        response = await getMission(event);
      }
    } else if (event.httpMethod === 'POST') {
      response = await createMission(event);
    } else if (event.httpMethod === 'PUT') {
      response = await updateMission(event);
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    // Add CORS headers to response
    response.headers = { ...response.headers, ...headers };
    return response;

  } catch (error) {
    console.error('Mission API error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
